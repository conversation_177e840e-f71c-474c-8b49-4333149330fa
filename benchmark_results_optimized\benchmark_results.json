{"speed": {"fastllama": {"seq512_batch1": {"avg_time": 1.1469865640004475, "std_time": 0.16076787637921547, "times": [1.37325119972229, 1.0145487785339355, 1.0531597137451172]}, "seq512_batch2": {"avg_time": 1.0596636136372883, "std_time": 0.018709990956827387, "times": [1.0794687271118164, 1.0649569034576416, 1.0345652103424072]}, "seq1024_batch1": {"avg_time": 1.0783439477284749, "std_time": 0.022665349128417334, "times": [1.1091692447662354, 1.070542335510254, 1.0553202629089355]}, "seq1024_batch2": {"avg_time": 1.1489628156026204, "std_time": 0.0174835885805199, "times": [1.1735255718231201, 1.1342284679412842, 1.139134407043457]}}, "baseline": {"seq512_batch1": {"avg_time": 1.4943989912668865, "std_time": 0.3400274707716889, "times": [1.0153212547302246, 1.769874095916748, 1.6980016231536865]}, "seq512_batch2": {"avg_time": 5.365502993265788, "std_time": 0.044150018721798044, "times": [5.393579006195068, 5.399762392044067, 5.3031675815582275]}, "seq1024_batch1": {"avg_time": 5.249150673548381, "std_time": 0.03006684733547233, "times": [5.209042072296143, 5.2569780349731445, 5.2814319133758545]}, "seq1024_batch2": {"avg_time": 11.477415084838867, "std_time": 0.12941476975913713, "times": [11.648436784744263, 11.33545994758606, 11.44834852218628]}}, "speedup": {"seq512_batch1": 1.3028914532832343, "seq512_batch2": 5.06340212517889, "seq1024_batch1": 4.867788876272441, "seq1024_batch2": 9.989370351223307}}, "memory": {"fastllama": {"seq512_batch1": {"gpu_allocated_gb": 6.251631259918213, "gpu_reserved_gb": 6.513671875, "gpu_max_allocated_gb": 6.495747089385986, "gpu_max_reserved_gb": 6.513671875, "cpu_rss_gb": 1.9712905883789062, "cpu_vms_gb": 10.322113037109375, "cpu_percent": 6.195244392669749, "system_total_gb": 31.819416046142578, "system_available_gb": 15.584541320800781, "system_used_percent": 51.0}, "seq512_batch2": {"gpu_allocated_gb": 6.251639366149902, "gpu_reserved_gb": 6.515625, "gpu_max_allocated_gb": 6.495755195617676, "gpu_max_reserved_gb": 6.515625, "cpu_rss_gb": 2.3646507263183594, "cpu_vms_gb": 10.767131805419922, "cpu_percent": 7.431471158645045, "system_total_gb": 31.819416046142578, "system_available_gb": 15.233085632324219, "system_used_percent": 52.1}, "seq1024_batch1": {"gpu_allocated_gb": 6.251663684844971, "gpu_reserved_gb": 6.513671875, "gpu_max_allocated_gb": 6.495763301849365, "gpu_max_reserved_gb": 6.513671875, "cpu_rss_gb": 2.364696502685547, "cpu_vms_gb": 10.765174865722656, "cpu_percent": 7.431615021647185, "system_total_gb": 31.819416046142578, "system_available_gb": 15.108386993408203, "system_used_percent": 52.5}, "seq1024_batch2": {"gpu_allocated_gb": 6.251679420471191, "gpu_reserved_gb": 6.51953125, "gpu_max_allocated_gb": 6.495762825012207, "gpu_max_reserved_gb": 6.51953125, "cpu_rss_gb": 1.5502586364746094, "cpu_vms_gb": 9.808326721191406, "cpu_percent": 4.872052441900627, "system_total_gb": 31.819416046142578, "system_available_gb": 16.02701187133789, "system_used_percent": 49.6}}, "baseline": {"seq512_batch1": {"gpu_allocated_gb": 5.15371036529541, "gpu_reserved_gb": 5.349609375, "gpu_max_allocated_gb": 5.242588996887207, "gpu_max_reserved_gb": 5.349609375, "cpu_rss_gb": 1.5499725341796875, "cpu_vms_gb": 8.635051727294922, "cpu_percent": 4.87115329813725, "system_total_gb": 31.819416046142578, "system_available_gb": 16.070167541503906, "system_used_percent": 49.5}, "seq512_batch2": {"gpu_allocated_gb": 5.1537184715271, "gpu_reserved_gb": 5.544921875, "gpu_max_allocated_gb": 5.331471920013428, "gpu_max_reserved_gb": 5.544921875, "cpu_rss_gb": 1.3058280944824219, "cpu_vms_gb": 8.580253601074219, "cpu_percent": 4.1038719648053545, "system_total_gb": 31.819416046142578, "system_available_gb": 16.162425994873047, "system_used_percent": 49.2}, "seq1024_batch1": {"gpu_allocated_gb": 5.154206275939941, "gpu_reserved_gb": 5.5625, "gpu_max_allocated_gb": 5.331963539123535, "gpu_max_reserved_gb": 5.5625, "cpu_rss_gb": 1.9856910705566406, "cpu_vms_gb": 9.318061828613281, "cpu_percent": 6.240501295426391, "system_total_gb": 31.819416046142578, "system_available_gb": 15.488250732421875, "system_used_percent": 51.3}, "seq1024_batch2": {"gpu_allocated_gb": 5.154222011566162, "gpu_reserved_gb": 5.931640625, "gpu_max_allocated_gb": 5.507775783538818, "gpu_max_reserved_gb": 5.931640625, "cpu_rss_gb": 1.9856910705566406, "cpu_vms_gb": 9.687923431396484, "cpu_percent": 6.240501295426391, "system_total_gb": 31.819416046142578, "system_available_gb": 15.596012115478516, "system_used_percent": 51.0}}, "memory_savings": {"seq512_batch1": -0.23903420490197558, "seq512_batch2": -0.21837933183774805, "seq1024_batch1": -0.2182685148138006, "seq1024_batch2": -0.17938040332473265}}, "early_exit": {"exit_rates": {"seq512_thresh0.7": 0.0, "seq512_thresh0.8": 0.0, "seq512_thresh0.9": 0.0, "seq1024_thresh0.7": 0.0, "seq1024_thresh0.8": 0.0, "seq1024_thresh0.9": 0.0}, "speedups": {"seq512_thresh0.7": 0.0, "seq512_thresh0.8": 0.0, "seq512_thresh0.9": 0.0, "seq1024_thresh0.7": 0.0, "seq1024_thresh0.8": 0.0, "seq1024_thresh0.9": 0.0}, "confidence_scores": {"seq512_thresh0.7": 0, "seq512_thresh0.8": 0, "seq512_thresh0.9": 0, "seq1024_thresh0.7": 0, "seq1024_thresh0.8": 0, "seq1024_thresh0.9": 0}}, "config": {"device": "cuda", "hidden_size": 2048, "intermediate_size": 11008, "num_attention_heads": 16, "num_key_value_heads": 4, "num_hidden_layers": 16, "vocab_size": 32000, "max_position_embeddings": 1048576, "rope_theta": 10000.0, "rope_scaling": {"type": "linear", "factor": 256.0}, "hidden_act": "silu", "rms_norm_eps": 1e-06, "local_attention_window": 512, "sparse_attention_stride": 8, "compression_ratio": 20, "local_layers": [1, 2, 3, 4, 5, 6, 7, 8], "sparse_layers": [9, 10, 11, 12, 13, 14, 15, 16], "hierarchical_layers": [17, 18, 19, 20, 21, 22, 23, 24], "full_attention_layers": [25, 26, 27, 28, 29, 30, 31, 32], "early_exit_layers": [2, 4, 8], "confidence_threshold": 0.8, "enable_early_exit": true, "compression_encoder_layers": 4, "enable_context_compression": true, "progressive_compression": true, "use_gradient_checkpointing": true, "gradient_checkpointing_ratio": 0.5, "use_mixed_precision": true, "kv_cache_quantization": true, "parameter_sharing": true, "initializer_range": 0.02, "use_cache": true, "pad_token_id": null, "bos_token_id": 1, "eos_token_id": 2, "use_flash_attention": true, "use_kernel_fusion": true, "enable_speculative_decoding": true, "max_batch_size": 32, "dynamic_batching": true, "memory_aware_batching": true, "output_attentions": false, "output_hidden_states": false, "use_return_dict": true}, "test_config": {"sequence_lengths": [512, 1024], "batch_sizes": [1, 2], "confidence_thresholds": [0.7, 0.8, 0.9], "num_runs": 3, "device": "cuda"}}