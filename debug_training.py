"""
Debug script to investigate FastLLaMA training issues.
"""

import os
import sys
import torch
import logging
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.training import FastLLaMATrainer, TrainingArguments
from fastllama.data import DataConfig, StreamingTextDataset
from transformers import AutoTokenizer

def setup_logging():
    """Setup basic logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def debug_data_loading():
    """Debug data loading and tokenization."""
    logger = setup_logging()
    logger.info("🔍 Debugging data loading...")

    # Create data configuration (same as in your script)
    data_config = DataConfig(
        dataset_name="HuggingFaceTB/smollm-corpus",
        dataset_config="fineweb-edu-dedup",
        tokenizer_name="D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer",
        max_length=1024,
        batch_size=2,
        streaming=True,
        num_workers=2,
        min_length=256
    )

    # Create tokenizer
    tokenizer = AutoTokenizer.from_pretrained("D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    logger.info(f"Tokenizer vocab size: {tokenizer.vocab_size}")
    logger.info(f"Tokenizer pad token: {tokenizer.pad_token}")
    logger.info(f"Tokenizer eos token: {tokenizer.eos_token}")

    # Create dataset
    train_dataset = StreamingTextDataset(data_config, tokenizer, split="train")

    # Check first few samples
    logger.info("🔍 Checking first few data samples...")
    sample_count = 0
    for i, sample in enumerate(train_dataset):
        if sample_count >= 3:
            break

        logger.info(f"\n--- Sample {i+1} ---")
        logger.info(f"Input IDs shape: {sample['input_ids'].shape}")
        logger.info(f"Labels shape: {sample['labels'].shape}")
        logger.info(f"Attention mask shape: {sample['attention_mask'].shape}")

        # Check if input_ids and labels are identical (they should be for causal LM)
        if torch.equal(sample['input_ids'], sample['labels']):
            logger.info("✅ Input IDs and labels are identical (correct for causal LM)")
        else:
            logger.warning("❌ Input IDs and labels are different!")

        # Check for special tokens
        input_ids = sample['input_ids']
        logger.info(f"First 10 tokens: {input_ids[:10].tolist()}")
        logger.info(f"Last 10 tokens: {input_ids[-10:].tolist()}")

        # Decode a portion to see actual text
        decoded_text = tokenizer.decode(input_ids[:50], skip_special_tokens=False)
        logger.info(f"Decoded text (first 50 tokens): {decoded_text}")

        # Check for padding tokens
        pad_count = (input_ids == tokenizer.pad_token_id).sum().item()
        logger.info(f"Number of padding tokens: {pad_count}")

        sample_count += 1

    return data_config, tokenizer

def debug_model_forward():
    """Debug model forward pass."""
    logger = setup_logging()
    logger.info("🔍 Debugging model forward pass...")

    # Create a simple model configuration
    config = FastLLaMAConfig(
        hidden_size=768,
        intermediate_size=768*4,
        num_attention_heads=8,
        num_key_value_heads=4,
        num_hidden_layers=8,
        vocab_size=64811 + 8,  # Your tokenizer vocab size
        max_position_embeddings=2048*16,

        # Disable complex features for debugging
        enable_context_compression=False,
        enable_early_exit=False,
        use_gradient_checkpointing=False,
        use_mixed_precision=False,

        # Set early exit layers to empty to avoid validation error
        early_exit_layers=[],
    )

    model = FastLLaMAModel(config)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)

    # Create dummy input
    batch_size = 2
    seq_length = 256
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_length)).to(device)
    labels = input_ids.clone()

    logger.info(f"Input shape: {input_ids.shape}")
    logger.info(f"Labels shape: {labels.shape}")

    # Forward pass
    model.train()
    outputs = model(input_ids=input_ids, labels=labels)

    logger.info(f"Loss: {outputs['loss'].item()}")
    logger.info(f"Logits shape: {outputs['logits'].shape}")

    # Check if loss is reasonable for random data
    expected_loss = torch.log(torch.tensor(config.vocab_size, dtype=torch.float))
    logger.info(f"Expected loss for random data: {expected_loss.item()}")

    if abs(outputs['loss'].item() - expected_loss.item()) < 1.0:
        logger.info("✅ Loss is reasonable for random data")
    else:
        logger.warning(f"❌ Loss seems unusual. Expected ~{expected_loss.item()}, got {outputs['loss'].item()}")

    return model, config

def debug_optimizer_and_gradients():
    """Debug optimizer and gradient flow."""
    logger = setup_logging()
    logger.info("🔍 Debugging optimizer and gradients...")

    # Get model and config from previous function
    model, config = debug_model_forward()
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # Create optimizer
    optimizer = torch.optim.AdamW(model.parameters(), lr=5e-4, weight_decay=0.01)

    # Create dummy batch
    batch_size = 2
    seq_length = 256
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_length)).to(device)
    labels = input_ids.clone()

    # Training step
    model.train()
    optimizer.zero_grad()

    outputs = model(input_ids=input_ids, labels=labels)
    loss = outputs['loss']

    logger.info(f"Initial loss: {loss.item()}")

    # Backward pass
    loss.backward()

    # Check gradients
    total_norm = 0
    param_count = 0
    zero_grad_count = 0

    for name, param in model.named_parameters():
        if param.grad is not None:
            param_norm = param.grad.data.norm(2)
            total_norm += param_norm.item() ** 2
            param_count += 1

            if param_norm.item() < 1e-8:
                zero_grad_count += 1
        else:
            logger.warning(f"No gradient for parameter: {name}")

    total_norm = total_norm ** (1. / 2)

    logger.info(f"Total gradient norm: {total_norm}")
    logger.info(f"Parameters with gradients: {param_count}")
    logger.info(f"Parameters with near-zero gradients: {zero_grad_count}")

    if total_norm < 1e-6:
        logger.warning("❌ Gradients are very small - possible vanishing gradient problem")
    elif total_norm > 100:
        logger.warning("❌ Gradients are very large - possible exploding gradient problem")
    else:
        logger.info("✅ Gradient norms look reasonable")

    # Optimizer step
    torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
    optimizer.step()

    # Check if parameters actually changed
    param_changes = []
    for name, param in model.named_parameters():
        if param.grad is not None:
            param_changes.append(param.grad.data.norm(2).item())

    avg_param_change = sum(param_changes) / len(param_changes) if param_changes else 0
    logger.info(f"Average parameter change: {avg_param_change}")

    return model, optimizer

def main():
    """Main debugging function."""
    logger = setup_logging()
    logger.info("🚀 Starting FastLLaMA training debug...")

    try:
        # Debug data loading
        data_config, tokenizer = debug_data_loading()

        # Debug model forward pass
        model, config = debug_model_forward()

        # Debug optimizer and gradients
        model, optimizer = debug_optimizer_and_gradients()

        logger.info("✅ All debugging checks completed!")

    except Exception as e:
        logger.error(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
