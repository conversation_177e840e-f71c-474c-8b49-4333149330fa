"""
<PERSON>ript to copy weights from original LLaMA models (HuggingFace) to FastLLaMA models.

This script handles the weight mapping between the original LLaMA architecture
and our FastLLaMA architecture, accounting for differences in layer names
and additional components like early exit heads.
"""

import torch
import logging
import argparse
from pathlib import Path
from typing import Dict, Any, Optional
from transformers import LlamaForCausalLM, LlamaConfig, AutoTokenizer

# FastLLaMA imports
from fastllama import FastLLaMAConfig, FastLLaMAModel


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_weight_mapping() -> Dict[str, str]:
    """Create mapping from LLaMA weight names to FastLLaMA weight names."""
    mapping = {
        # Embeddings
        "model.embed_tokens.weight": "embed_tokens.embed_tokens.weight",

        # Final layer norm
        "model.norm.weight": "norm.weight",

        # Language modeling head
        "lm_head.weight": "lm_head.weight",
    }

    # Layer mappings (will be generated dynamically based on num_layers)
    return mapping


def map_layer_weights(layer_idx: int) -> Dict[str, str]:
    """Create weight mapping for a specific transformer layer."""
    llama_prefix = f"model.layers.{layer_idx}"
    fastllama_prefix = f"layers.{layer_idx}"

    mapping = {
        # Self attention
        f"{llama_prefix}.self_attn.q_proj.weight": f"{fastllama_prefix}.self_attn.q_proj.weight",
        f"{llama_prefix}.self_attn.k_proj.weight": f"{fastllama_prefix}.self_attn.k_proj.weight",
        f"{llama_prefix}.self_attn.v_proj.weight": f"{fastllama_prefix}.self_attn.v_proj.weight",
        f"{llama_prefix}.self_attn.o_proj.weight": f"{fastllama_prefix}.self_attn.o_proj.weight",

        # MLP
        f"{llama_prefix}.mlp.gate_proj.weight": f"{fastllama_prefix}.mlp.gate_proj.weight",
        f"{llama_prefix}.mlp.up_proj.weight": f"{fastllama_prefix}.mlp.up_proj.weight",
        f"{llama_prefix}.mlp.down_proj.weight": f"{fastllama_prefix}.mlp.down_proj.weight",

        # Layer norms
        f"{llama_prefix}.input_layernorm.weight": f"{fastllama_prefix}.input_layernorm.weight",
        f"{llama_prefix}.post_attention_layernorm.weight": f"{fastllama_prefix}.post_attention_layernorm.weight",
    }

    # Add bias terms if they exist (some models have them)
    bias_mapping = {
        f"{llama_prefix}.self_attn.q_proj.bias": f"{fastllama_prefix}.self_attn.q_proj.bias",
        f"{llama_prefix}.self_attn.k_proj.bias": f"{fastllama_prefix}.self_attn.k_proj.bias",
        f"{llama_prefix}.self_attn.v_proj.bias": f"{fastllama_prefix}.self_attn.v_proj.bias",
        f"{llama_prefix}.self_attn.o_proj.bias": f"{fastllama_prefix}.self_attn.o_proj.bias",
        f"{llama_prefix}.mlp.gate_proj.bias": f"{fastllama_prefix}.mlp.gate_proj.bias",
        f"{llama_prefix}.mlp.up_proj.bias": f"{fastllama_prefix}.mlp.up_proj.bias",
        f"{llama_prefix}.mlp.down_proj.bias": f"{fastllama_prefix}.mlp.down_proj.bias",
    }
    mapping.update(bias_mapping)

    return mapping


def convert_llama_config_to_fastllama(llama_config: LlamaConfig) -> FastLLaMAConfig:
    """Convert LLaMA config to FastLLaMA config."""
    logger = logging.getLogger(__name__)

    # Create FastLLaMA config with LLaMA parameters
    fastllama_config = FastLLaMAConfig(
        # Core architecture (from LLaMA)
        hidden_size=llama_config.hidden_size,
        intermediate_size=llama_config.intermediate_size,
        num_attention_heads=llama_config.num_attention_heads,
        num_key_value_heads=getattr(llama_config, 'num_key_value_heads', llama_config.num_attention_heads),
        num_hidden_layers=llama_config.num_hidden_layers,
        vocab_size=llama_config.vocab_size,
        max_position_embeddings=llama_config.max_position_embeddings,

        # RoPE configuration
        rope_theta=getattr(llama_config, 'rope_theta', 10000.0),

        # Normalization
        rms_norm_eps=llama_config.rms_norm_eps,

        # FastLLaMA specific features (can be enabled later)
        enable_context_compression=False,  # Disable initially
        enable_early_exit=False,  # Disable initially
        use_gradient_checkpointing=False,
        use_mixed_precision=True,

        # Conservative hierarchical attention settings
        local_attention_window=512,
        sparse_attention_stride=8,
        compression_ratio=20,

        # Early exit (disabled initially)
        early_exit_layers=[],
        confidence_threshold=0.8,
    )

    logger.info(f"Converted LLaMA config to FastLLaMA config:")
    logger.info(f"  - Hidden size: {fastllama_config.hidden_size}")
    logger.info(f"  - Layers: {fastllama_config.num_hidden_layers}")
    logger.info(f"  - Attention heads: {fastllama_config.num_attention_heads}")
    logger.info(f"  - KV heads: {fastllama_config.num_key_value_heads}")
    logger.info(f"  - Vocab size: {fastllama_config.vocab_size}")

    return fastllama_config


def copy_weights(
    llama_model: LlamaForCausalLM,
    fastllama_model: FastLLaMAModel,
    strict: bool = False
) -> Dict[str, Any]:
    """Copy weights from LLaMA model to FastLLaMA model."""
    logger = logging.getLogger(__name__)

    llama_state_dict = llama_model.state_dict()
    fastllama_state_dict = fastllama_model.state_dict()

    # Create complete weight mapping
    weight_mapping = create_weight_mapping()

    # Add layer-specific mappings
    num_layers = fastllama_model.config.num_hidden_layers
    for layer_idx in range(num_layers):
        layer_mapping = map_layer_weights(layer_idx)
        weight_mapping.update(layer_mapping)

    # Copy weights
    copied_weights = []
    missing_weights = []
    size_mismatches = []

    for llama_key, fastllama_key in weight_mapping.items():
        if llama_key in llama_state_dict and fastllama_key in fastllama_state_dict:
            llama_weight = llama_state_dict[llama_key]
            fastllama_weight = fastllama_state_dict[fastllama_key]

            if llama_weight.shape == fastllama_weight.shape:
                fastllama_state_dict[fastllama_key] = llama_weight.clone()
                copied_weights.append(fastllama_key)
            else:
                size_mismatches.append({
                    'key': fastllama_key,
                    'llama_shape': llama_weight.shape,
                    'fastllama_shape': fastllama_weight.shape
                })
        else:
            if llama_key not in llama_state_dict:
                missing_weights.append(f"LLaMA missing: {llama_key}")
            if fastllama_key not in fastllama_state_dict:
                missing_weights.append(f"FastLLaMA missing: {fastllama_key}")

    # Load the updated state dict
    missing_keys, unexpected_keys = fastllama_model.load_state_dict(fastllama_state_dict, strict=strict)

    # Report results
    logger.info(f"Weight copying results:")
    logger.info(f"  - Successfully copied: {len(copied_weights)} weights")
    logger.info(f"  - Size mismatches: {len(size_mismatches)}")
    logger.info(f"  - Missing weights: {len(missing_weights)}")
    logger.info(f"  - Missing keys in FastLLaMA: {len(missing_keys)}")
    logger.info(f"  - Unexpected keys: {len(unexpected_keys)}")

    if size_mismatches:
        logger.warning("Size mismatches found:")
        for mismatch in size_mismatches[:5]:  # Show first 5
            logger.warning(f"  {mismatch['key']}: {mismatch['llama_shape']} -> {mismatch['fastllama_shape']}")

    if missing_weights:
        logger.warning(f"Missing weights (first 10): {missing_weights[:10]}")

    return {
        'copied_weights': copied_weights,
        'size_mismatches': size_mismatches,
        'missing_weights': missing_weights,
        'missing_keys': missing_keys,
        'unexpected_keys': unexpected_keys
    }


def main():
    """Main function to copy LLaMA weights to FastLLaMA."""
    parser = argparse.ArgumentParser(description="Copy LLaMA weights to FastLLaMA model")

    parser.add_argument(
        "--llama_model_name",
        type=str,
        default="HuggingFaceTB/SmolLM2-360M-Instruct",
        help="HuggingFace LLaMA model name or path"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="./fastllama_from_llama",
        help="Output directory for FastLLaMA model"
    )
    parser.add_argument(
        "--device",
        type=str,
        default="auto",
        help="Device to load models on (auto, cpu, cuda)"
    )
    parser.add_argument(
        "--enable_early_exit",
        action="store_true",
        help="Enable early exit in FastLLaMA model"
    )
    parser.add_argument(
        "--early_exit_layers",
        type=int,
        nargs="+",
        default=[12, 18, 24],
        help="Layers to add early exit heads"
    )
    parser.add_argument(
        "--strict",
        action="store_true",
        help="Use strict weight loading"
    )

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging()
    logger.info(f"🚀 Starting LLaMA to FastLLaMA weight conversion")
    logger.info(f"Source model: {args.llama_model_name}")
    logger.info(f"Output directory: {args.output_dir}")

    # Determine device
    if args.device == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(args.device)
    logger.info(f"Using device: {device}")

    try:
        # 1. Load original LLaMA model
        logger.info("📥 Loading original LLaMA model...")
        llama_model = LlamaForCausalLM.from_pretrained(
            args.llama_model_name,
            torch_dtype=torch.float16 if device.type == "cuda" else torch.float32,
            device_map="auto" if device.type == "cuda" else None,
        )
        llama_config = llama_model.config

        logger.info(f"✅ LLaMA model loaded:")
        logger.info(f"  - Parameters: {sum(p.numel() for p in llama_model.parameters())/1e6:.1f}M")
        logger.info(f"  - Layers: {llama_config.num_hidden_layers}")
        logger.info(f"  - Hidden size: {llama_config.hidden_size}")

        # 2. Load tokenizer
        logger.info("📝 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(args.llama_model_name)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # 3. Create FastLLaMA config
        logger.info("⚙️ Creating FastLLaMA configuration...")
        fastllama_config = convert_llama_config_to_fastllama(llama_config)

        # Enable early exit if requested
        if args.enable_early_exit:
            # Filter early exit layers to be within model bounds
            valid_layers = [l for l in args.early_exit_layers if l < fastllama_config.num_hidden_layers]
            fastllama_config.enable_early_exit = True
            fastllama_config.early_exit_layers = valid_layers
            logger.info(f"✅ Early exit enabled at layers: {valid_layers}")

        # 4. Create FastLLaMA model
        logger.info("🧠 Creating FastLLaMA model...")
        fastllama_model = FastLLaMAModel(fastllama_config)
        fastllama_model.to(device)

        logger.info(f"✅ FastLLaMA model created:")
        logger.info(f"  - Parameters: {sum(p.numel() for p in fastllama_model.parameters())/1e6:.1f}M")

        # 5. Copy weights
        logger.info("🔄 Copying weights from LLaMA to FastLLaMA...")
        copy_results = copy_weights(llama_model, fastllama_model, strict=args.strict)

        # 6. Save FastLLaMA model
        logger.info("💾 Saving FastLLaMA model...")
        output_path = Path(args.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Save model weights
        torch.save(fastllama_model.state_dict(), output_path / "pytorch_model.bin")

        # Save config
        fastllama_config.save_pretrained(output_path)

        # Save tokenizer
        tokenizer.save_pretrained(output_path)

        # Save conversion report
        import json
        with open(output_path / "conversion_report.json", "w") as f:
            json.dump({
                "source_model": args.llama_model_name,
                "copied_weights": len(copy_results['copied_weights']),
                "size_mismatches": len(copy_results['size_mismatches']),
                "missing_weights": len(copy_results['missing_weights']),
                "early_exit_enabled": args.enable_early_exit,
                "early_exit_layers": fastllama_config.early_exit_layers if args.enable_early_exit else [],
            }, f, indent=2)

        logger.info(f"✅ FastLLaMA model saved to {output_path}")
        logger.info(f"📊 Conversion summary:")
        logger.info(f"  - Copied weights: {len(copy_results['copied_weights'])}")
        logger.info(f"  - Size mismatches: {len(copy_results['size_mismatches'])}")
        logger.info(f"  - Missing weights: {len(copy_results['missing_weights'])}")

        return fastllama_model, tokenizer, copy_results

    except Exception as e:
        logger.error(f"❌ Conversion failed: {e}")
        raise


if __name__ == "__main__":
    model, tokenizer, results = main()
    print("\n🎉 LLaMA to FastLLaMA conversion completed!")
    print(f"✅ Model ready for training or inference")
