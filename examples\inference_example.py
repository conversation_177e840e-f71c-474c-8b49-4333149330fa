"""
Example inference script for FastLLaMA.

Demonstrates how to use the FastLLaMA model for text generation with
optimized inference features including early exit, speculative decoding,
and memory-efficient generation.
"""

import torch
import argparse
import time
import logging
import os
from typing import List, Dict, Any

# FastLLaMA imports
from fastllama import FastLLaMAConfig, FastLLaMAModel, FastLLaMAInferenceEngine
from fastllama.inference import GenerationConfig
from fastllama.utils import MemoryOptimizer, get_memory_stats

# Tokenizer imports
from transformers import AutoTokenizer


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_model_and_config(args) -> tuple:
    """Create FastLLaMA model and configuration for inference."""
    logger = logging.getLogger(__name__)
    tokenizer = None

    if args.model_path:
        logger.info(f"Loading model and config from {args.model_path}")

        # Check if model path exists
        if not os.path.exists(args.model_path):
            raise FileNotFoundError(f"Model path does not exist: {args.model_path}")

        # Load tokenizer if available
        try:
            tokenizer = AutoTokenizer.from_pretrained(args.model_path)
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            logger.info(f"Loaded tokenizer with vocab size: {tokenizer.vocab_size}")
        except Exception as e:
            logger.warning(f"Could not load tokenizer: {e}")
            tokenizer = None

        # Check if config file exists
        config_path = os.path.join(args.model_path, 'config.json')
        if not os.path.exists(config_path):
            logger.warning("No config.json found, using command line arguments")
            config = create_config_from_args(args)
        else:
            try:
                # Load configuration from JSON file
                import json
                with open(config_path, 'r') as f:
                    config_dict = json.load(f)

                # Create config from dictionary
                config = FastLLaMAConfig(**config_dict)
                logger.info("Loaded configuration from config.json")

                # Override with command line arguments if specified
                if args.enable_early_exit:
                    config.enable_early_exit = True
                elif args.disable_early_exit:
                    config.enable_early_exit = False

                if args.enable_speculative_decoding:
                    config.enable_speculative_decoding = True
                elif args.disable_speculative_decoding:
                    config.enable_speculative_decoding = False

                if args.confidence_threshold is not None:
                    config.confidence_threshold = args.confidence_threshold

            except Exception as e:
                logger.warning(f"Error loading config: {e}. Using command line arguments")
                config = create_config_from_args(args)
    else:
        config = create_config_from_args(args)

    # Create model
    model = FastLLaMAModel(config)

    # Load pretrained weights if specified
    if args.model_path:
        model_file = os.path.join(args.model_path, 'pytorch_model.bin')
        if not os.path.exists(model_file):
            raise FileNotFoundError(f"Model weights file does not exist: {model_file}")

        logger.info(f"Loading model weights from {model_file}")
        state_dict = torch.load(model_file, map_location='cpu')

        # Load state dict with strict=False to handle missing keys gracefully
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)

        if missing_keys:
            logger.warning(f"Missing keys in state_dict: {missing_keys}")
        if unexpected_keys:
            logger.warning(f"Unexpected keys in state_dict: {unexpected_keys}")

        logger.info("Model weights loaded successfully")

    return model, config, tokenizer


def create_config_from_args(args) -> FastLLaMAConfig:
    """Create configuration from command line arguments."""
    # Determine early exit setting
    enable_early_exit = False
    if args.enable_early_exit:
        enable_early_exit = True
    elif args.disable_early_exit:
        enable_early_exit = False

    # Determine speculative decoding setting
    enable_speculative_decoding = False
    if args.enable_speculative_decoding:
        enable_speculative_decoding = True
    elif args.disable_speculative_decoding:
        enable_speculative_decoding = False

    # Set confidence threshold
    confidence_threshold = args.confidence_threshold if args.confidence_threshold is not None else 0.8

    return FastLLaMAConfig(
        hidden_size=args.hidden_size,
        intermediate_size=args.intermediate_size,
        num_attention_heads=args.num_attention_heads,
        num_key_value_heads=args.num_key_value_heads,
        num_hidden_layers=args.num_hidden_layers,
        vocab_size=args.vocab_size,
        max_position_embeddings=args.max_position_embeddings,

        # Enable inference optimizations
        enable_context_compression=True,
        enable_early_exit=enable_early_exit,
        kv_cache_quantization=True,
        use_flash_attention=True,
        enable_speculative_decoding=enable_speculative_decoding,

        # Hierarchical attention for long contexts
        local_attention_window=512,
        sparse_attention_stride=8,
        compression_ratio=20,

        # Early exit configuration
        early_exit_layers=[3, 5] if args.num_hidden_layers >= 6 else [2, 4],  # Adjust based on layer count
        confidence_threshold=confidence_threshold,
    )


def create_sample_inputs(args) -> List[str]:
    """Create sample input texts for generation."""
    if args.input_text:
        return [args.input_text]

    # Default sample inputs
    sample_inputs = [
        "The future of artificial intelligence is",
        "In a world where technology advances rapidly,",
        "The key to understanding complex systems lies in",
        "Once upon a time, in a distant galaxy,",
        "The most important lesson I learned was",
    ]

    return sample_inputs[:args.num_samples]


def tokenize_inputs(inputs: List[str], tokenizer=None, max_length: int = 50) -> torch.Tensor:
    """Tokenize input texts using the provided tokenizer or dummy implementation."""
    if tokenizer is not None:
        # Use actual tokenizer
        tokenized = tokenizer(
            inputs,
            max_length=max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        return tokenized["input_ids"]
    else:
        # Fallback to dummy tokenization
        tokenized = []
        vocab_size = 50257  # Default vocab size

        for text in inputs:
            # Convert text to token IDs (dummy implementation)
            tokens = [hash(char) % vocab_size for char in text[:max_length]]
            tokens = tokens + [1] * (max_length - len(tokens))  # Pad to fixed length
            tokenized.append(tokens)

        return torch.tensor(tokenized, dtype=torch.long)


def detokenize_outputs(token_ids: torch.Tensor, tokenizer=None) -> List[str]:
    """Detokenize output tokens using the provided tokenizer or dummy implementation."""
    if tokenizer is not None:
        # Use actual tokenizer
        return tokenizer.batch_decode(token_ids, skip_special_tokens=True)
    else:
        # Fallback to dummy detokenization
        outputs = []

        for sequence in token_ids:
            # Convert token IDs back to text (dummy implementation)
            text = "".join([chr(65 + (token % 26)) for token in sequence[:100]])  # Limit output
            outputs.append(text)

        return outputs


def run_inference_benchmark(
    inference_engine: FastLLaMAInferenceEngine,
    input_ids: torch.Tensor,
    generation_config: GenerationConfig,
    num_runs: int = 5
) -> Dict[str, Any]:
    """Run inference benchmark and collect performance metrics."""
    logger = logging.getLogger(__name__)

    times = []
    memory_stats = []
    generation_stats = []

    for run in range(num_runs):
        logger.info(f"Running inference benchmark {run + 1}/{num_runs}")

        # Clear cache before each run
        inference_engine.clear_cache()
        torch.cuda.empty_cache() if torch.cuda.is_available() else None

        # Record start time and memory
        start_time = time.time()
        # start_memory = get_memory_stats()  # Not used in current implementation

        # Run inference
        outputs = inference_engine.generate(
            input_ids=input_ids,
            generation_config=generation_config
        )

        # Record end time and memory
        end_time = time.time()
        end_memory = get_memory_stats()

        # Calculate metrics
        elapsed_time = end_time - start_time
        times.append(elapsed_time)
        memory_stats.append(end_memory)
        generation_stats.append(outputs.generation_stats)

        logger.info(f"Run {run + 1} completed in {elapsed_time:.2f}s")

    # Calculate average metrics
    avg_time = sum(times) / len(times)
    avg_tokens_per_second = sum(stats["tokens_per_second"] for stats in generation_stats) / len(generation_stats)
    avg_memory = sum(stats.get("gpu_allocated_gb", 0) for stats in memory_stats) / len(memory_stats)

    return {
        "avg_time": avg_time,
        "avg_tokens_per_second": avg_tokens_per_second,
        "avg_memory_gb": avg_memory,
        "times": times,
        "generation_stats": generation_stats,
        "memory_stats": memory_stats,
    }


def main():
    parser = argparse.ArgumentParser(description="FastLLaMA Inference Example")

    # Model configuration
    parser.add_argument("--model_path", type=str, help="Path to pretrained model weights")
    parser.add_argument("--hidden_size", type=int, default=1024)
    parser.add_argument("--intermediate_size", type=int, default=2048)
    parser.add_argument("--num_attention_heads", type=int, default=8)
    parser.add_argument("--num_key_value_heads", type=int, default=2)
    parser.add_argument("--num_hidden_layers", type=int, default=8)
    parser.add_argument("--vocab_size", type=int, default=32000)
    parser.add_argument("--max_position_embeddings", type=int, default=2048)

    # Generation configuration
    parser.add_argument("--max_new_tokens", type=int, default=100)
    parser.add_argument("--temperature", type=float, default=1.0)
    parser.add_argument("--top_k", type=int, default=50)
    parser.add_argument("--top_p", type=float, default=0.9)
    parser.add_argument("--do_sample", action="store_true", default=True)

    # FastLLaMA features
    parser.add_argument("--enable_early_exit", action="store_true", help="Enable early exit (overrides config)")
    parser.add_argument("--disable_early_exit", action="store_true", help="Disable early exit (overrides config)")
    parser.add_argument("--confidence_threshold", type=float, help="Confidence threshold for early exit (overrides config)")
    parser.add_argument("--enable_speculative_decoding", action="store_true", help="Enable speculative decoding (overrides config)")
    parser.add_argument("--disable_speculative_decoding", action="store_true", help="Disable speculative decoding (overrides config)")

    # Input configuration
    parser.add_argument("--input_text", type=str, help="Input text for generation")
    parser.add_argument("--num_samples", type=int, default=3, help="Number of sample inputs to generate")

    # Benchmark configuration
    parser.add_argument("--benchmark", action="store_true", help="Run performance benchmark")
    parser.add_argument("--num_benchmark_runs", type=int, default=5)

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging()
    logger.info("Starting FastLLaMA inference example")

    # Check device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # Create model and configuration
    model, config, tokenizer = create_model_and_config(args)
    logger.info(f"Created FastLLaMA model with {sum(p.numel() for p in model.parameters())} parameters")

    # Setup memory optimizer for inference
    memory_optimizer = MemoryOptimizer(model, config)
    memory_optimizer.optimize_for_inference()
    logger.info("Applied inference optimizations")

    # Create inference engine
    inference_engine = FastLLaMAInferenceEngine(
        model=model,
        config=config,
        device=device,
        enable_speculative_decoding=args.enable_speculative_decoding
    )

    # Create generation configuration
    generation_config = GenerationConfig(
        max_new_tokens=args.max_new_tokens,
        temperature=args.temperature,
        top_k=args.top_k,
        top_p=args.top_p,
        do_sample=args.do_sample,
        enable_early_exit=config.enable_early_exit,  # Use config setting
        confidence_threshold=config.confidence_threshold,  # Use config setting
        use_cache=False,  # Disable cache for now
    )

    # Create sample inputs
    input_texts = create_sample_inputs(args)
    logger.info(f"Created {len(input_texts)} input samples")

    # Tokenize inputs
    input_ids = tokenize_inputs(input_texts, tokenizer, max_length=50)
    input_ids = input_ids.to(device)

    logger.info("Starting text generation...")

    # Run inference
    start_time = time.time()
    outputs = inference_engine.generate(
        input_ids=input_ids,
        generation_config=generation_config
    )
    end_time = time.time()

    # Detokenize outputs
    generated_texts = detokenize_outputs(outputs.sequences, tokenizer)

    # Display results
    logger.info(f"Generation completed in {end_time - start_time:.2f} seconds")

    for i, (input_text, generated_text) in enumerate(zip(input_texts, generated_texts)):
        print(f"\n--- Sample {i + 1} ---")
        print(f"Input: {input_text}")
        print(f"Generated: {generated_text}")

    # Display generation statistics
    if outputs.generation_stats:
        print(f"\n--- Generation Statistics ---")
        for key, value in outputs.generation_stats.items():
            print(f"{key}: {value}")

    # Display early exit information
    if outputs.early_exit_info:
        print(f"\n--- Early Exit Information ---")
        print(f"Exits used: {outputs.early_exit_info.get('exits_used', [])}")
        confidence_scores = outputs.early_exit_info.get('confidence_scores', [])
        if confidence_scores:
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            print(f"Average confidence: {avg_confidence:.3f}")
        else:
            print("Average confidence: N/A (no early exits)")

    # Run benchmark if requested
    if args.benchmark:
        logger.info("Running performance benchmark...")
        benchmark_results = run_inference_benchmark(
            inference_engine,
            input_ids,
            generation_config,
            args.num_benchmark_runs
        )

        print(f"\n--- Benchmark Results ---")
        print(f"Average time: {benchmark_results['avg_time']:.2f}s")
        print(f"Average tokens/second: {benchmark_results['avg_tokens_per_second']:.2f}")
        print(f"Average memory usage: {benchmark_results['avg_memory_gb']:.2f}GB")

    # Display memory usage
    final_memory = get_memory_stats()
    print(f"\n--- Memory Usage ---")
    for key, value in final_memory.items():
        print(f"{key}: {value:.2f}")

    # Get inference engine statistics
    engine_stats = inference_engine.get_generation_stats()
    print(f"\n--- Engine Statistics ---")
    for key, value in engine_stats.items():
        print(f"{key}: {value}")

    logger.info("Inference example completed successfully!")


if __name__ == "__main__":
    main()
