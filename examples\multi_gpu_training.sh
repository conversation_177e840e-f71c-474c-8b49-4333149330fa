#!/bin/bash

# Multi-GPU training script for FastLLaMA
# Supports both single-node multi-GPU and multi-node training

echo "🚀 FastLLaMA Multi-GPU Training Setup"

# Configuration
DATASET_NAME="c4"
DATASET_CONFIG="en"
TOKENIZER_NAME="t5-base"
OUTPUT_DIR="./fastllama_c4_multigpu_output"
NUM_GPUS=4  # Adjust based on your setup
MASTER_PORT=29500

# Training hyperparameters
GLOBAL_BATCH_SIZE=128
PER_DEVICE_BATCH_SIZE=2
GRADIENT_ACCUMULATION_STEPS=$((GLOBAL_BATCH_SIZE / (NUM_GPUS * PER_DEVICE_BATCH_SIZE)))
MAX_SEQ_LENGTH=2048
LEARNING_RATE=1e-4
NUM_EPOCHS=5

# Model configuration (Large model)
HIDDEN_SIZE=4096
NUM_LAYERS=32
NUM_HEADS=32
NUM_KV_HEADS=8

echo "📊 Multi-GPU Training Configuration:"
echo "  GPUs: $NUM_GPUS"
echo "  Global Batch Size: $GLOBAL_BATCH_SIZE"
echo "  Per-Device Batch Size: $PER_DEVICE_BATCH_SIZE"
echo "  Gradient Accumulation Steps: $GRADIENT_ACCUMULATION_STEPS"
echo "  Model: ${HIDDEN_SIZE}d, ${NUM_LAYERS} layers"

# Set environment variables for distributed training
export CUDA_VISIBLE_DEVICES=0,1,2,3
export TOKENIZERS_PARALLELISM=false
export NCCL_DEBUG=INFO
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Create output directory
mkdir -p $OUTPUT_DIR

# Function for single-node multi-GPU training
single_node_training() {
    echo "🔥 Starting single-node multi-GPU training..."
    
    torchrun \
        --nproc_per_node=$NUM_GPUS \
        --master_port=$MASTER_PORT \
        examples/train_hf_streaming.py \
        --dataset_name $DATASET_NAME \
        --dataset_config $DATASET_CONFIG \
        --tokenizer_name $TOKENIZER_NAME \
        --output_dir $OUTPUT_DIR \
        --max_seq_length $MAX_SEQ_LENGTH \
        --per_device_train_batch_size $PER_DEVICE_BATCH_SIZE \
        --per_device_eval_batch_size $PER_DEVICE_BATCH_SIZE \
        --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
        --learning_rate $LEARNING_RATE \
        --num_train_epochs $NUM_EPOCHS \
        --hidden_size $HIDDEN_SIZE \
        --num_hidden_layers $NUM_LAYERS \
        --num_attention_heads $NUM_HEADS \
        --num_key_value_heads $NUM_KV_HEADS \
        --streaming \
        --enable_context_compression \
        --enable_early_exit \
        --use_gradient_checkpointing \
        --use_mixed_precision \
        --bf16 \
        --local_attention_window 512 \
        --sparse_attention_stride 8 \
        --compression_ratio 16 \
        --early_exit_layers 8 16 24 \
        --confidence_threshold 0.75 \
        --warmup_steps 5000 \
        --eval_steps 2000 \
        --save_steps 5000 \
        --logging_steps 200 \
        --dataloader_num_workers 8 \
        --save_total_limit 5 \
        --foundation_phase_ratio 0.6 \
        --long_context_phase_ratio 0.3 \
        --efficiency_phase_ratio 0.1 \
        --weight_decay 0.1 \
        --max_steps 100000
}

# Function for multi-node training (example)
multi_node_training() {
    echo "🌐 Multi-node training configuration:"
    echo "  Set MASTER_ADDR and MASTER_PORT environment variables"
    echo "  Run this script on each node with appropriate NODE_RANK"
    
    # Example for node 0:
    # export MASTER_ADDR="*************"
    # export MASTER_PORT="29500"
    # export NODE_RANK=0
    # export WORLD_SIZE=8  # Total number of GPUs across all nodes
    
    torchrun \
        --nnodes=2 \
        --nproc_per_node=$NUM_GPUS \
        --node_rank=${NODE_RANK:-0} \
        --master_addr=${MASTER_ADDR:-"localhost"} \
        --master_port=${MASTER_PORT:-29500} \
        examples/train_hf_streaming.py \
        --dataset_name $DATASET_NAME \
        --dataset_config $DATASET_CONFIG \
        --tokenizer_name $TOKENIZER_NAME \
        --output_dir $OUTPUT_DIR \
        --max_seq_length $MAX_SEQ_LENGTH \
        --per_device_train_batch_size $PER_DEVICE_BATCH_SIZE \
        --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
        --learning_rate $LEARNING_RATE \
        --num_train_epochs $NUM_EPOCHS \
        --hidden_size $HIDDEN_SIZE \
        --num_hidden_layers $NUM_LAYERS \
        --num_attention_heads $NUM_HEADS \
        --num_key_value_heads $NUM_KV_HEADS \
        --streaming \
        --enable_context_compression \
        --enable_early_exit \
        --use_gradient_checkpointing \
        --use_mixed_precision \
        --bf16
}

# Check if multi-node training is requested
if [ "$1" = "multi-node" ]; then
    multi_node_training
else
    single_node_training
fi

echo "✅ Multi-GPU training completed!"
echo "📁 Results saved to: $OUTPUT_DIR"
echo "📊 Check logs for training metrics and performance"
