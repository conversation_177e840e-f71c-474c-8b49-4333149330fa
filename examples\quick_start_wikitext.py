"""
Quick Start: Train <PERSON>LLaMA on WikiText-2 with streaming.

This is a minimal example to get started quickly with FastLLaMA training
using HuggingFace datasets streaming.
"""

import os
import sys
import torch
import logging
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.training import FastLLaMATrainer, TrainingArguments
from fastllama.utils import get_memory_stats


def setup_logging():
    """Setup basic logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def main():
    logger = setup_logging()
    logger.info("🚀 Quick Start: FastLLaMA Training on WikiText-2")

    # Configuration
    output_dir = "./fastllama_wikitext_output"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # 1. Create datasets and tokenizer
    logger.info("📚 Loading WikiText-2 dataset with streaming...")
    from fastllama.data import DataConfig

    # Create data configuration
    data_config = DataConfig(
        dataset_name="wikitext",
        dataset_config="wikitext-2-raw-v1",
        tokenizer_name="gpt2",
        max_length=512,  # Shorter for quick training
        batch_size=2,
        streaming=True,
        num_workers=2,
        text_column="text",
        filter_empty=True,
        min_length=10,
    )

    # Create tokenizer
    from transformers import AutoTokenizer
    tokenizer = AutoTokenizer.from_pretrained("gpt2")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    logger.info(f"✅ Data configuration set up. Tokenizer vocab size: {tokenizer.vocab_size}")
    logger.info("📝 Note: Using FastLLaMADataLoader for enhanced data loading")

    # 2. Create FastLLaMA model
    logger.info("🧠 Creating FastLLaMA model...")
    config = FastLLaMAConfig(
        # Small model for quick training
        hidden_size=512,
        intermediate_size=1024,
        num_attention_heads=8,
        num_key_value_heads=2,
        num_hidden_layers=6,
        vocab_size=tokenizer.vocab_size,
        max_position_embeddings=2048,

        # Enable key FastLLaMA features
        enable_context_compression=True,
        enable_early_exit=True,
        use_gradient_checkpointing=True,
        use_mixed_precision=True,

        # Hierarchical attention
        local_attention_window=256,
        sparse_attention_stride=4,
        compression_ratio=8,

        # Early exit
        early_exit_layers=[3, 5],
        confidence_threshold=0.6,
    )

    model = FastLLaMAModel(config)
    model.to(device)

    num_params = sum(p.numel() for p in model.parameters())
    logger.info(f"✅ Model created with {num_params/1e6:.1f}M parameters")

    # 3. Setup training arguments
    logger.info("⚙️ Setting up training configuration...")
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=1,  # Quick training
        max_steps=200,  # Limit steps for quick training
        per_device_train_batch_size=2,
        per_device_eval_batch_size=2,
        gradient_accumulation_steps=2,
        learning_rate=5e-4,
        weight_decay=0.01,
        warmup_steps=100,

        # Memory optimizations
        use_mixed_precision=True,
        gradient_checkpointing=True,
        max_grad_norm=1.0,

        # Sequence length scheduling
        initial_seq_length=256,
        max_seq_length=512,
        seq_length_warmup_steps=200,

        # Evaluation and logging
        eval_steps=500,
        save_steps=500,
        logging_steps=10,

        # Phase-based training (simplified)
        foundation_phase_ratio=0.8,
        long_context_phase_ratio=0.15,
        efficiency_phase_ratio=0.05,

        # Early exit training
        early_exit_loss_weight=0.1,
        confidence_loss_weight=0.05,

        # Data configuration (for FastLLaMADataLoader)
        dataset_name=data_config.dataset_name,
        dataset_config=data_config.dataset_config,
        tokenizer_name=data_config.tokenizer_name,
        streaming=data_config.streaming,
        text_column=data_config.text_column,
        filter_empty=data_config.filter_empty,
        min_length=data_config.min_length,
    )

    # 4. Create trainer with FastLLaMADataLoader
    logger.info("🏋️ Creating trainer with FastLLaMADataLoader...")
    trainer = FastLLaMATrainer(
        model=model,
        config=config,
        args=training_args,
        tokenizer=tokenizer,
        data_config=data_config,  # Use our custom data config
    )

    logger.info("✅ Trainer created with enhanced FastLLaMADataLoader integration")

    # 5. Start training
    logger.info("🚀 Starting training...")
    initial_memory = get_memory_stats()
    logger.info(f"Initial memory usage: {initial_memory}")

    try:
        training_metrics = trainer.train()

        logger.info("🎉 Training completed successfully!")
        logger.info(f"Final training metrics: {training_metrics}")

        # Print memory usage
        final_memory = get_memory_stats()
        logger.info(f"Final memory usage: {final_memory}")

        # Save model
        model_path = Path(output_dir) / "final_model"
        model_path.mkdir(parents=True, exist_ok=True)
        torch.save(model.state_dict(), model_path / "pytorch_model.bin")
        config.save_pretrained(model_path)
        tokenizer.save_pretrained(model_path)

        logger.info(f"✅ Model saved to {model_path}")

        return model, tokenizer, training_metrics

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        raise

    finally:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


if __name__ == "__main__":
    model, tokenizer, metrics = main()
    print("\n🎉 Quick start training completed!")
    print(f"📊 Final loss: {metrics}")
    print(f"🧠 Model parameters: {sum(p.numel() for p in model.parameters())/1e6:.1f}M")
    print("🚀 Ready for inference or further training!")
