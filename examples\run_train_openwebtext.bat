@echo off
setlocal enabledelayedexpansion

echo 🚀 Starting FastLLaMA training on OpenWebText

:: Set environment variables
set CUDA_VISIBLE_DEVICES=0
set TOKENIZERS_PARALLELISM=false

:: Training configuration
set DATASET_NAME=HuggingFaceTB/smollm-corpus
set DATASET_CONFIG=fineweb-edu-dedup
set TOKENIZER_NAME=gpt2
set OUTPUT_DIR=.\fastllama_smollm_corpus_output
set MAX_SEQ_LENGTH=2048
set BATCH_SIZE=2
set GRADIENT_ACCUMULATION_STEPS=8
set LEARNING_RATE=3e-4
set NUM_EPOCHS=3

:: Model configuration
set HIDDEN_SIZE=2048
set NUM_LAYERS=24
set NUM_HEADS=32
set NUM_KV_HEADS=8

echo 📊 Training Configuration:
echo   Dataset: %DATASET_NAME%
echo   Output: %OUTPUT_DIR%
echo   Sequence Length: %MAX_SEQ_LENGTH%
echo   Batch Size: %BATCH_SIZE%
echo   Learning Rate: %LEARNING_RATE%
echo   Model Size: %HIDDEN_SIZE%d, %NUM_LAYERS% layers

:: Create output directory
if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

:: Run training
python examples/train_hf_streaming.py ^
    --dataset_name %DATASET_NAME% ^
    --tokenizer_name %TOKENIZER_NAME% ^
    --output_dir %OUTPUT_DIR% ^
    --max_seq_length %MAX_SEQ_LENGTH% ^
    --per_device_train_batch_size %BATCH_SIZE% ^
    --per_device_eval_batch_size %BATCH_SIZE% ^
    --gradient_accumulation_steps %GRADIENT_ACCUMULATION_STEPS% ^
    --learning_rate %LEARNING_RATE% ^
    --num_train_epochs %NUM_EPOCHS% ^
    --hidden_size %HIDDEN_SIZE% ^
    --num_hidden_layers %NUM_LAYERS% ^
    --num_attention_heads %NUM_HEADS% ^
    --num_key_value_heads %NUM_KV_HEADS% ^
    --streaming ^
    --enable_context_compression ^
    --enable_early_exit ^
    --use_gradient_checkpointing ^
    --use_mixed_precision ^
    --local_attention_window 512 ^
    --sparse_attention_stride 8 ^
    --compression_ratio 16 ^
    --early_exit_layers 8 12 16 20 ^
    --confidence_threshold 0.7 ^
    --warmup_steps 2000 ^
    --eval_steps 1000 ^
    --save_steps 2000 ^
    --logging_steps 100 ^
    --dataloader_num_workers 8 ^
    --save_total_limit 3 ^
    --foundation_phase_ratio 0.7 ^
    --long_context_phase_ratio 0.2 ^
    --efficiency_phase_ratio 0.1

echo ✅ Training completed!
echo 📁 Results saved to: %OUTPUT_DIR%

endlocal