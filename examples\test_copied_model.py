"""
Simple test script for the copied FastLLaMA model.
"""

import torch
from fastllama import FastLLaMAModel, FastLLaMAConfig
import json

def main():
    print("🧪 Testing copied FastLLaMA model...")

    # Load the copied model
    with open('fastllama_with_early_exit/config.json', 'r') as f:
        config_dict = json.load(f)

    config = FastLLaMAConfig(**config_dict)
    model = FastLLaMAModel(config)
    model.load_state_dict(torch.load('fastllama_with_early_exit/pytorch_model.bin', map_location='cpu'))

    print(f'✅ Model loaded successfully!')
    print(f'📊 Model info:')
    print(f'  - Vocab size: {config.vocab_size}')
    print(f'  - Hidden size: {config.hidden_size}')
    print(f'  - Layers: {config.num_hidden_layers}')
    print(f'  - Parameters: {sum(p.numel() for p in model.parameters())/1e6:.1f}M')
    print(f'  - Early exit enabled: {config.enable_early_exit}')
    print(f'  - Early exit layers: {config.early_exit_layers}')

    # Test forward pass with correct vocab size
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    print(f'🔧 Using device: {device}')

    batch_size, seq_len = 2, 10
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len)).to(device)
    print(f'📥 Input shape: {input_ids.shape}')

    with torch.no_grad():
        output = model(input_ids)

    print(f'✅ Forward pass successful!')
    print(f'📤 Output shape: {output["logits"].shape}')
    print(f'🔑 Output keys: {list(output.keys())}')

    if config.enable_early_exit and 'early_exit_outputs' in output:
        early_exits = output['early_exit_outputs']
        if early_exits is not None:
            print(f'🚪 Early exit outputs: {len(early_exits)} layers')
            for i, exit_output in enumerate(early_exits):
                if exit_output is not None:
                    print(f'  - Layer {config.early_exit_layers[i]}: {exit_output["logits"].shape}')
        else:
            print(f'🚪 Early exit outputs: None (not triggered)')

    print("🎉 Test completed successfully!")

if __name__ == "__main__":
    main()
