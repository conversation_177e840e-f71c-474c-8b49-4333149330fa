"""
Test script for HuggingFace datasets with FastLLaMA.

This script helps you test different datasets and configurations
before starting full training.
"""

import os
import sys
import torch
import logging
from typing import Dict, Any
import time

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastllama.data import create_dataloader, get_dataset
from transformers import AutoTokenizer


def setup_logging():
    """Setup logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def test_dataset_loading(dataset_name: str, dataset_config: str = None, **kwargs) -> Dict[str, Any]:
    """Test loading a specific dataset."""
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"🔍 Testing dataset: {dataset_name}")
        if dataset_config:
            logger.info(f"   Config: {dataset_config}")

        # Get dataset info
        dataset_info = get_dataset(dataset_name, dataset_config, **kwargs)

        if "error" in dataset_info:
            logger.error(f"❌ Failed to load dataset: {dataset_info['error']}")
            return {"success": False, "error": dataset_info["error"]}

        logger.info(f"✅ Dataset loaded successfully")
        logger.info(f"   Features: {dataset_info.get('features', 'Unknown')}")
        logger.info(f"   Text columns: {dataset_info.get('text_columns', [])}")

        # Show sample data
        if dataset_info.get("sample_examples"):
            logger.info("📝 Sample examples:")
            for i, example in enumerate(dataset_info["sample_examples"][:2]):
                text_cols = dataset_info.get("text_columns", ["text"])
                for col in text_cols:
                    if col in example:
                        text = str(example[col])[:200] + "..." if len(str(example[col])) > 200 else str(example[col])
                        logger.info(f"   Example {i+1} ({col}): {text}")

        return {"success": True, "info": dataset_info}

    except Exception as e:
        logger.error(f"❌ Error testing dataset {dataset_name}: {e}")
        return {"success": False, "error": str(e)}


def test_dataloader_creation(
    dataset_name: str,
    dataset_config: str = None,
    tokenizer_name: str = "gpt2",
    max_length: int = 512,
    batch_size: int = 2,
    **kwargs
) -> Dict[str, Any]:
    """Test creating dataloaders."""
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"🔧 Testing dataloader creation...")
        logger.info(f"   Dataset: {dataset_name}")
        logger.info(f"   Tokenizer: {tokenizer_name}")
        logger.info(f"   Max length: {max_length}")
        logger.info(f"   Batch size: {batch_size}")

        start_time = time.time()

        # Create dataloaders
        train_dataloader, eval_dataloader, tokenizer = create_dataloader(
            dataset_name=dataset_name,
            dataset_config=dataset_config,
            tokenizer_name=tokenizer_name,
            max_length=max_length,
            batch_size=batch_size,
            streaming=True,
            **kwargs
        )

        creation_time = time.time() - start_time
        logger.info(f"✅ Dataloaders created in {creation_time:.2f}s")
        logger.info(f"   Tokenizer vocab size: {tokenizer.vocab_size}")

        # Test loading a few batches
        logger.info("🔄 Testing batch loading...")

        batch_times = []
        for i, batch in enumerate(train_dataloader):
            if i >= 3:  # Test first 3 batches
                break

            batch_start = time.time()

            # Check batch structure
            logger.info(f"   Batch {i+1}:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    logger.info(f"     {key}: {value.shape} ({value.dtype})")
                else:
                    logger.info(f"     {key}: {type(value)}")

            batch_time = time.time() - batch_start
            batch_times.append(batch_time)
            logger.info(f"     Load time: {batch_time:.3f}s")

        avg_batch_time = sum(batch_times) / len(batch_times) if batch_times else 0
        logger.info(f"📊 Average batch load time: {avg_batch_time:.3f}s")

        return {
            "success": True,
            "creation_time": creation_time,
            "avg_batch_time": avg_batch_time,
            "vocab_size": tokenizer.vocab_size
        }

    except Exception as e:
        logger.error(f"❌ Error creating dataloader: {e}")
        return {"success": False, "error": str(e)}


def test_tokenizer_compatibility(tokenizer_name: str, sample_texts: list) -> Dict[str, Any]:
    """Test tokenizer with sample texts."""
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"🔤 Testing tokenizer: {tokenizer_name}")

        tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        logger.info(f"   Vocab size: {tokenizer.vocab_size}")
        logger.info(f"   Special tokens: pad={tokenizer.pad_token_id}, eos={tokenizer.eos_token_id}")

        # Test tokenization
        for i, text in enumerate(sample_texts[:2]):
            tokens = tokenizer(text, return_tensors="pt", truncation=True, max_length=100)
            decoded = tokenizer.decode(tokens["input_ids"][0], skip_special_tokens=True)

            logger.info(f"   Sample {i+1}:")
            logger.info(f"     Original: {text[:100]}...")
            logger.info(f"     Tokens: {tokens['input_ids'].shape[1]} tokens")
            logger.info(f"     Decoded: {decoded[:100]}...")

        return {"success": True, "vocab_size": tokenizer.vocab_size}

    except Exception as e:
        logger.error(f"❌ Error testing tokenizer: {e}")
        return {"success": False, "error": str(e)}


def main():
    logger = setup_logging()
    logger.info("🧪 FastLLaMA Dataset Testing Suite")

    # Test configurations
    test_configs = [
        {
            "name": "WikiText-2 (Small, Reliable)",
            "dataset_name": "wikitext",
            "dataset_config": "wikitext-2-raw-v1",
            "tokenizer_name": "gpt2",
            "max_length": 512,
            "batch_size": 2
        },
        {
            "name": "C4 (Large, English)",
            "dataset_name": "c4",
            "dataset_config": "en",
            "tokenizer_name": "t5-base",
            "max_length": 1024,
            "batch_size": 1
        },
        {
            "name": "OpenWebText (Large, GPT-style)",
            "dataset_name": "openwebtext",
            "dataset_config": None,
            "tokenizer_name": "gpt2",
            "max_length": 1024,
            "batch_size": 1
        }
    ]

    results = {}

    for config in test_configs:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 Testing: {config['name']}")
        logger.info(f"{'='*60}")

        config_name = config["name"]
        results[config_name] = {}

        # Test 1: Dataset loading
        logger.info("\n📚 Step 1: Testing dataset loading...")
        dataset_result = test_dataset_loading(
            config["dataset_name"],
            config.get("dataset_config")
        )
        results[config_name]["dataset_loading"] = dataset_result

        if not dataset_result["success"]:
            logger.warning(f"⚠️ Skipping further tests for {config_name}")
            continue

        # Test 2: Tokenizer compatibility
        logger.info("\n🔤 Step 2: Testing tokenizer compatibility...")
        sample_texts = []
        if dataset_result.get("info", {}).get("sample_examples"):
            for example in dataset_result["info"]["sample_examples"]:
                text_cols = dataset_result["info"].get("text_columns", ["text"])
                for col in text_cols:
                    if col in example and example[col]:
                        sample_texts.append(str(example[col]))
                        break

        if sample_texts:
            tokenizer_result = test_tokenizer_compatibility(
                config["tokenizer_name"],
                sample_texts
            )
            results[config_name]["tokenizer"] = tokenizer_result
        else:
            logger.warning("⚠️ No sample texts found for tokenizer testing")

        # Test 3: Dataloader creation
        logger.info("\n🔧 Step 3: Testing dataloader creation...")
        dataloader_result = test_dataloader_creation(
            config["dataset_name"],
            config.get("dataset_config"),
            config["tokenizer_name"],
            config["max_length"],
            config["batch_size"]
        )
        results[config_name]["dataloader"] = dataloader_result

    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("📊 TESTING SUMMARY")
    logger.info(f"{'='*60}")

    for config_name, config_results in results.items():
        logger.info(f"\n🧪 {config_name}:")

        for test_name, test_result in config_results.items():
            status = "✅ PASS" if test_result.get("success") else "❌ FAIL"
            logger.info(f"   {test_name}: {status}")

            if not test_result.get("success") and "error" in test_result:
                logger.info(f"     Error: {test_result['error']}")

    # Recommendations
    logger.info(f"\n💡 RECOMMENDATIONS:")

    successful_configs = [
        name for name, results in results.items()
        if all(test.get("success", False) for test in results.values())
    ]

    if successful_configs:
        logger.info(f"✅ Ready for training: {', '.join(successful_configs)}")
        logger.info(f"🚀 Start with: python examples/quick_start_wikitext.py")
    else:
        logger.info(f"⚠️ No configurations passed all tests")
        logger.info(f"🔧 Check your internet connection and HuggingFace access")

    return results


if __name__ == "__main__":
    results = main()
    print(f"\n🎯 Testing completed! Check logs for detailed results.")
