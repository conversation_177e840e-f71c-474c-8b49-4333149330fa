"""
Simple test to verify the learning rate scheduler fix.
"""

import torch
import logging
from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.training.optimizers import FastLLaMAOptimizer

def test_lr_scheduler():
    """Test the learning rate scheduler directly."""
    print("🧪 Testing FastLLaMA learning rate scheduler...")
    
    # Create a simple model
    config = FastLLaMAConfig(
        hidden_size=64,
        intermediate_size=128,
        num_attention_heads=2,
        num_key_value_heads=1,
        num_hidden_layers=2,
        vocab_size=100,
        max_position_embeddings=128,
    )
    
    model = FastLLaMAModel(config)
    print(f"Model created with {sum(p.numel() for p in model.parameters())} parameters")
    
    # Create optimizer
    optimizer = FastLLaMAOptimizer(
        model=model,
        learning_rate=1e-3,
        warmup_steps=5,
        total_steps=20,
        scheduler_type="cosine_with_warmup",
    )
    
    print(f"Optimizer created:")
    print(f"  - Initial LR: {optimizer.get_lr()}")
    print(f"  - Warmup steps: {optimizer.warmup_steps}")
    print(f"  - Total steps: {optimizer.total_steps}")
    
    # Test scheduler progression
    print("\n📈 Testing scheduler progression:")
    for step in range(25):
        # Simulate training step
        dummy_input = torch.randint(0, 100, (1, 10))
        outputs = model(dummy_input)
        loss = outputs['loss']
        
        # Backward pass
        loss.backward()
        
        # Optimizer step
        success = optimizer.step(loss)
        lr = optimizer.get_lr()
        
        if step % 2 == 0:  # Log every 2 steps
            print(f"  Step {step:2d}: lr={lr:.6f}, loss={loss.item():.4f}, success={success}")
        
        optimizer.zero_grad()
    
    print("\n✅ Learning rate scheduler test completed!")

if __name__ == "__main__":
    test_lr_scheduler()
