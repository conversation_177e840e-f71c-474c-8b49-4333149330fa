"""
Test script for copying weights from a real small LLaMA-like model to FastLLaMA.

This script demonstrates weight copying using a small, publicly available model
that doesn't require authentication.
"""

import torch
import logging
import argparse
from pathlib import Path
from typing import Dict, Any

# FastLLaMA imports
from fastllama import FastLLaMAConfig, FastLLaMAModel


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_weight_mapping(num_layers: int) -> Dict[str, str]:
    """Create mapping from model weight names to FastLLaMA weight names."""
    mapping = {
        # Embeddings
        "embed_tokens.weight": "embed_tokens.embed_tokens.weight",

        # Final layer norm
        "norm.weight": "norm.weight",

        # Language modeling head
        "lm_head.weight": "lm_head.weight",
    }

    # Layer mappings
    for layer_idx in range(num_layers):
        layer_mapping = {
            # Self attention
            f"layers.{layer_idx}.self_attn.q_proj.weight": f"layers.{layer_idx}.self_attn.q_proj.weight",
            f"layers.{layer_idx}.self_attn.k_proj.weight": f"layers.{layer_idx}.self_attn.k_proj.weight",
            f"layers.{layer_idx}.self_attn.v_proj.weight": f"layers.{layer_idx}.self_attn.v_proj.weight",
            f"layers.{layer_idx}.self_attn.o_proj.weight": f"layers.{layer_idx}.self_attn.o_proj.weight",

            # MLP
            f"layers.{layer_idx}.mlp.gate_proj.weight": f"layers.{layer_idx}.mlp.gate_proj.weight",
            f"layers.{layer_idx}.mlp.up_proj.weight": f"layers.{layer_idx}.mlp.up_proj.weight",
            f"layers.{layer_idx}.mlp.down_proj.weight": f"layers.{layer_idx}.mlp.down_proj.weight",

            # Layer norms
            f"layers.{layer_idx}.input_layernorm.weight": f"layers.{layer_idx}.input_layernorm.weight",
            f"layers.{layer_idx}.post_attention_layernorm.weight": f"layers.{layer_idx}.post_attention_layernorm.weight",
        }
        mapping.update(layer_mapping)

    return mapping


def copy_weights_flexible(
    source_state_dict: Dict[str, torch.Tensor],
    target_model: FastLLaMAModel,
    strict: bool = False
) -> Dict[str, Any]:
    """Copy weights from source state dict to FastLLaMA model with flexible mapping."""
    logger = logging.getLogger(__name__)

    target_state_dict = target_model.state_dict()

    # Create weight mapping
    weight_mapping = create_weight_mapping(target_model.config.num_hidden_layers)

    # Copy weights
    copied_weights = []
    missing_weights = []
    size_mismatches = []

    for source_key, target_key in weight_mapping.items():
        if source_key in source_state_dict and target_key in target_state_dict:
            source_weight = source_state_dict[source_key]
            target_weight = target_state_dict[target_key]

            if source_weight.shape == target_weight.shape:
                target_state_dict[target_key] = source_weight.clone()
                copied_weights.append(target_key)
            else:
                size_mismatches.append({
                    'key': target_key,
                    'source_shape': source_weight.shape,
                    'target_shape': target_weight.shape
                })
        else:
            if source_key not in source_state_dict:
                missing_weights.append(f"Source missing: {source_key}")
            if target_key not in target_state_dict:
                missing_weights.append(f"Target missing: {target_key}")

    # Load the updated state dict
    missing_keys, unexpected_keys = target_model.load_state_dict(target_state_dict, strict=strict)

    # Report results
    logger.info(f"Weight copying results:")
    logger.info(f"  - Successfully copied: {len(copied_weights)} weights")
    logger.info(f"  - Size mismatches: {len(size_mismatches)}")
    logger.info(f"  - Missing weights: {len(missing_weights)}")
    logger.info(f"  - Missing keys in target: {len(missing_keys)}")
    logger.info(f"  - Unexpected keys: {len(unexpected_keys)}")

    if size_mismatches:
        logger.warning("Size mismatches found:")
        for mismatch in size_mismatches[:5]:  # Show first 5
            logger.warning(f"  {mismatch['key']}: {mismatch['source_shape']} -> {mismatch['target_shape']}")

    return {
        'copied_weights': copied_weights,
        'size_mismatches': size_mismatches,
        'missing_weights': missing_weights,
        'missing_keys': missing_keys,
        'unexpected_keys': unexpected_keys
    }


def main():
    """Test weight copying with a saved model."""
    parser = argparse.ArgumentParser(description="Test weight copying to FastLLaMA")

    parser.add_argument(
        "--source_model_path",
        type=str,
        default="./test_weight_copy_output",
        help="Path to source model"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="./fastllama_copied_weights",
        help="Output directory for FastLLaMA model"
    )
    parser.add_argument(
        "--enable_early_exit",
        action="store_true",
        help="Enable early exit in FastLLaMA model"
    )
    parser.add_argument(
        "--early_exit_layers",
        type=int,
        nargs="+",
        default=[2, 3],
        help="Layers to add early exit heads"
    )

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging()
    logger.info(f"🚀 Testing weight copying to FastLLaMA")
    logger.info(f"Source model: {args.source_model_path}")
    logger.info(f"Output directory: {args.output_dir}")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    try:
        # 1. Load source model state dict
        logger.info("📥 Loading source model...")
        source_model_path = Path(args.source_model_path)

        if not source_model_path.exists():
            logger.error(f"Source model path does not exist: {source_model_path}")
            logger.info("Please run test_weight_copy.py first to create a test model")
            return

        # Load config
        config_path = source_model_path / "config.json"
        if config_path.exists():
            import json
            with open(config_path, 'r') as f:
                source_config = json.load(f)
            logger.info(f"✅ Loaded source config: {source_config['num_hidden_layers']} layers")
        else:
            logger.error("No config.json found in source model")
            return

        # Load state dict
        model_path = source_model_path / "pytorch_model.bin"
        if model_path.exists():
            source_state_dict = torch.load(model_path, map_location='cpu')
            logger.info(f"✅ Loaded source state dict with {len(source_state_dict)} keys")
        else:
            logger.error("No pytorch_model.bin found in source model")
            return

        # 2. Create FastLLaMA config
        logger.info("⚙️ Creating FastLLaMA configuration...")
        fastllama_config = FastLLaMAConfig(
            hidden_size=source_config['hidden_size'],
            intermediate_size=source_config['intermediate_size'],
            num_attention_heads=source_config['num_attention_heads'],
            num_key_value_heads=source_config['num_key_value_heads'],
            num_hidden_layers=source_config['num_hidden_layers'],
            vocab_size=source_config['vocab_size'],
            max_position_embeddings=source_config['max_position_embeddings'],
            rope_theta=source_config.get('rope_theta', 10000.0),
            rms_norm_eps=source_config.get('rms_norm_eps', 1e-6),

            # FastLLaMA features
            enable_context_compression=False,  # Start simple
            enable_early_exit=args.enable_early_exit,
            use_gradient_checkpointing=False,

            # Early exit configuration
            early_exit_layers=args.early_exit_layers if args.enable_early_exit else [],
            confidence_threshold=0.8,
        )

        logger.info(f"✅ FastLLaMA config created")

        # 3. Create FastLLaMA model
        logger.info("🧠 Creating FastLLaMA model...")
        fastllama_model = FastLLaMAModel(fastllama_config)
        fastllama_model.to(device)

        logger.info(f"✅ FastLLaMA model created:")
        logger.info(f"  - Parameters: {sum(p.numel() for p in fastllama_model.parameters())/1e6:.1f}M")

        # 4. Copy weights
        logger.info("🔄 Copying weights...")
        copy_results = copy_weights_flexible(source_state_dict, fastllama_model, strict=False)

        # 5. Test forward pass
        logger.info("🧪 Testing forward pass...")
        batch_size, seq_len = 2, 10
        input_ids = torch.randint(0, fastllama_config.vocab_size, (batch_size, seq_len)).to(device)

        with torch.no_grad():
            fastllama_output = fastllama_model(input_ids)

        logger.info(f"✅ Forward pass successful:")
        logger.info(f"  - Output shape: {fastllama_output['logits'].shape}")
        logger.info(f"  - Output keys: {list(fastllama_output.keys())}")

        # 6. Save FastLLaMA model
        logger.info("💾 Saving FastLLaMA model...")
        output_path = Path(args.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Save model weights
        torch.save(fastllama_model.state_dict(), output_path / "pytorch_model.bin")

        # Save config
        fastllama_config.save_pretrained(output_path)

        # Save conversion report
        import json
        with open(output_path / "conversion_report.json", "w") as f:
            json.dump({
                "source_model_path": str(args.source_model_path),
                "copied_weights": len(copy_results['copied_weights']),
                "size_mismatches": len(copy_results['size_mismatches']),
                "missing_weights": len(copy_results['missing_weights']),
                "early_exit_enabled": args.enable_early_exit,
                "early_exit_layers": fastllama_config.early_exit_layers,
            }, f, indent=2)

        logger.info(f"✅ FastLLaMA model saved to {output_path}")
        logger.info(f"📊 Conversion summary:")
        logger.info(f"  - Copied weights: {len(copy_results['copied_weights'])}")
        logger.info(f"  - Size mismatches: {len(copy_results['size_mismatches'])}")
        logger.info(f"  - Missing weights: {len(copy_results['missing_weights'])}")

        return fastllama_model, copy_results

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    model, results = main()
    print("\n🎉 Weight copying test completed!")
    print(f"✅ Successfully copied {len(results['copied_weights'])} weights")
    print(f"⚠️  Size mismatches: {len(results['size_mismatches'])}")
    print(f"⚠️  Missing weights: {len(results['missing_weights'])}")
    print("🚀 FastLLaMA model ready for use!")
