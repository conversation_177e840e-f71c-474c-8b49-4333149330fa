"""
Test script for the updated FastLLaMA trainer with FastLLaMADataLoader integration.

This script demonstrates the new trainer functionality that uses our custom
dataloader with enhanced features like dynamic batching and better preprocessing.
"""

import torch
import logging
from pathlib import Path

# FastLLaMA imports
from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.training import FastLLaMATrainer, TrainingArguments
from fastllama.data.dataloader import DataConfig
from fastllama.utils import get_memory_stats


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def main():
    """Main training function using updated trainer."""
    logger = setup_logging()
    logger.info("🚀 Testing updated FastLLaMA trainer with FastLLaMADataLoader")

    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # Output directory
    output_dir = "./fastllama_updated_trainer_test"
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # 1. Create FastLLaMA model configuration
    logger.info("🧠 Creating FastLLaMA model...")
    config = FastLLaMAConfig(
        # Small model for testing
        hidden_size=256,
        intermediate_size=512,
        num_attention_heads=4,
        num_key_value_heads=2,
        num_hidden_layers=4,
        vocab_size=50257,  # GPT-2 vocab size
        max_position_embeddings=1024,

        # Enable key FastLLaMA features
        enable_context_compression=True,
        enable_early_exit=True,
        use_gradient_checkpointing=True,
        use_mixed_precision=True,

        # Hierarchical attention
        local_attention_window=128,
        sparse_attention_stride=2,
        compression_ratio=4,

        # Early exit
        early_exit_layers=[2, 3],
        confidence_threshold=0.7,
    )

    model = FastLLaMAModel(config)
    model.to(device)

    num_params = sum(p.numel() for p in model.parameters())
    logger.info(f"✅ Model created with {num_params/1e6:.1f}M parameters")

    # 2. Setup data configuration
    logger.info("📊 Setting up data configuration...")
    data_config = DataConfig(
        dataset_name="wikitext",
        dataset_config="wikitext-2-raw-v1",
        tokenizer_name="gpt2",
        max_length=256,  # Shorter sequences for testing
        batch_size=2,
        streaming=True,
        num_workers=2,
        text_column="text",
        filter_empty=True,
        min_length=10,
    )

    # 3. Setup training arguments
    logger.info("⚙️ Setting up training configuration...")
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=1,
        max_steps=50,  # Very short training for testing
        per_device_train_batch_size=2,
        per_device_eval_batch_size=2,
        gradient_accumulation_steps=1,
        learning_rate=1e-4,
        weight_decay=0.01,
        warmup_steps=10,

        # Memory optimizations
        use_mixed_precision=True,
        gradient_checkpointing=True,
        max_grad_norm=1.0,

        # Sequence length scheduling
        initial_seq_length=256,
        max_seq_length=512,
        seq_length_warmup_steps=50,

        # Evaluation and logging
        eval_steps=25,
        save_steps=25,
        logging_steps=5,

        # Phase-based training (simplified)
        foundation_phase_ratio=0.8,
        long_context_phase_ratio=0.15,
        efficiency_phase_ratio=0.05,

        # Early exit training
        early_exit_loss_weight=0.1,
        confidence_loss_weight=0.05,

        # Data configuration (will be used by FastLLaMADataLoader)
        dataset_name=data_config.dataset_name,
        dataset_config=data_config.dataset_config,
        tokenizer_name=data_config.tokenizer_name,
        streaming=data_config.streaming,
        text_column=data_config.text_column,
        filter_empty=data_config.filter_empty,
        min_length=data_config.min_length,
    )

    # 4. Create trainer with data config
    logger.info("🏋️ Creating trainer with FastLLaMADataLoader...")
    trainer = FastLLaMATrainer(
        model=model,
        config=config,
        args=training_args,
        data_config=data_config,  # Pass our custom data config
    )

    logger.info("✅ Trainer created successfully with FastLLaMADataLoader integration")

    # 5. Test dataloader creation
    logger.info("🔍 Testing dataloader creation...")
    try:
        train_dataloader = trainer._create_dataloader(is_eval=False)
        eval_dataloader = trainer._create_dataloader(is_eval=True)
        
        logger.info(f"✅ Train dataloader created: batch_size={train_dataloader.batch_size}")
        logger.info(f"✅ Eval dataloader created: batch_size={eval_dataloader.batch_size}")
        
        # Test getting a batch
        logger.info("🔍 Testing batch retrieval...")
        train_iter = iter(train_dataloader)
        batch = next(train_iter)
        
        logger.info(f"✅ Batch retrieved successfully:")
        logger.info(f"   - input_ids shape: {batch['input_ids'].shape}")
        logger.info(f"   - attention_mask shape: {batch['attention_mask'].shape}")
        logger.info(f"   - labels shape: {batch['labels'].shape}")
        
    except Exception as e:
        logger.error(f"❌ Dataloader test failed: {e}")
        raise

    # 6. Start training
    logger.info("🚀 Starting training...")
    initial_memory = get_memory_stats()
    logger.info(f"Initial memory usage: {initial_memory}")

    try:
        training_metrics = trainer.train()

        logger.info("🎉 Training completed successfully!")
        logger.info(f"Final training metrics: {training_metrics}")

        # Print memory usage
        final_memory = get_memory_stats()
        logger.info(f"Final memory usage: {final_memory}")

        # Save model
        model_path = Path(output_dir) / "final_model"
        model_path.mkdir(parents=True, exist_ok=True)
        torch.save(model.state_dict(), model_path / "pytorch_model.bin")
        config.save_pretrained(model_path)

        logger.info(f"✅ Model saved to {model_path}")

        return model, training_metrics

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        raise

    finally:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


if __name__ == "__main__":
    try:
        model, metrics = main()
        print("\n🎉 Updated trainer test completed successfully!")
        print(f"📊 Final metrics: {metrics}")
        print(f"🧠 Model parameters: {sum(p.numel() for p in model.parameters())/1e6:.1f}M")
        print("✅ FastLLaMADataLoader integration working correctly!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
