"""
Test script for copying weights from smaller LLaMA-like models to FastLLaMA.

This script tests the weight copying functionality using smaller, publicly available models
that don't require authentication.
"""

import torch
import logging
from pathlib import Path

# FastLLaMA imports
from fastllama import FastLLaMAConfig, FastLLaMAModel


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_test_llama_model():
    """Create a small test LLaMA-like model for testing weight copying."""
    logger = logging.getLogger(__name__)

    # Create a small LLaMA-like model configuration
    class TestLLaMAConfig:
        def __init__(self):
            self.hidden_size = 512
            self.intermediate_size = 1024
            self.num_attention_heads = 8
            self.num_key_value_heads = 2
            self.num_hidden_layers = 4
            self.vocab_size = 1000
            self.max_position_embeddings = 2048
            self.rope_theta = 10000.0
            self.rms_norm_eps = 1e-6

    # Create a simple model structure similar to LLaMA
    class TestLLaMAModel(torch.nn.Module):
        def __init__(self, config):
            super().__init__()
            self.config = config

            # Embeddings
            self.embed_tokens = torch.nn.Embedding(config.vocab_size, config.hidden_size)

            # Layers
            self.layers = torch.nn.ModuleList([
                TestLLaMALayer(config) for _ in range(config.num_hidden_layers)
            ])

            # Final norm (use LayerNorm as substitute for RMSNorm)
            self.norm = torch.nn.LayerNorm(config.hidden_size, eps=config.rms_norm_eps)

            # LM head
            self.lm_head = torch.nn.Linear(config.hidden_size, config.vocab_size, bias=False)

    class TestLLaMALayer(torch.nn.Module):
        def __init__(self, config):
            super().__init__()
            self.config = config

            # Self attention
            self.self_attn = TestLLaMAAttention(config)

            # MLP
            self.mlp = TestLLaMAMLP(config)

            # Layer norms (use LayerNorm as substitute for RMSNorm)
            self.input_layernorm = torch.nn.LayerNorm(config.hidden_size, eps=config.rms_norm_eps)
            self.post_attention_layernorm = torch.nn.LayerNorm(config.hidden_size, eps=config.rms_norm_eps)

    class TestLLaMAAttention(torch.nn.Module):
        def __init__(self, config):
            super().__init__()
            self.config = config

            self.q_proj = torch.nn.Linear(config.hidden_size, config.num_attention_heads * 64, bias=False)
            self.k_proj = torch.nn.Linear(config.hidden_size, config.num_key_value_heads * 64, bias=False)
            self.v_proj = torch.nn.Linear(config.hidden_size, config.num_key_value_heads * 64, bias=False)
            self.o_proj = torch.nn.Linear(config.num_attention_heads * 64, config.hidden_size, bias=False)

    class TestLLaMAMLP(torch.nn.Module):
        def __init__(self, config):
            super().__init__()
            self.config = config

            self.gate_proj = torch.nn.Linear(config.hidden_size, config.intermediate_size, bias=False)
            self.up_proj = torch.nn.Linear(config.hidden_size, config.intermediate_size, bias=False)
            self.down_proj = torch.nn.Linear(config.intermediate_size, config.hidden_size, bias=False)

    config = TestLLaMAConfig()
    model = TestLLaMAModel(config)

    # Initialize with random weights
    for param in model.parameters():
        if param.dim() > 1:
            torch.nn.init.xavier_uniform_(param)
        else:
            torch.nn.init.zeros_(param)

    logger.info(f"Created test LLaMA model with {sum(p.numel() for p in model.parameters())/1e6:.1f}M parameters")

    return model, config


def create_weight_mapping_for_test(num_layers: int):
    """Create weight mapping for test models."""
    mapping = {
        # Embeddings
        "embed_tokens.weight": "embed_tokens.embed_tokens.weight",

        # Final layer norm
        "norm.weight": "norm.weight",

        # Language modeling head
        "lm_head.weight": "lm_head.weight",
    }

    # Layer mappings
    for layer_idx in range(num_layers):
        layer_mapping = {
            # Self attention
            f"layers.{layer_idx}.self_attn.q_proj.weight": f"layers.{layer_idx}.self_attn.q_proj.weight",
            f"layers.{layer_idx}.self_attn.k_proj.weight": f"layers.{layer_idx}.self_attn.k_proj.weight",
            f"layers.{layer_idx}.self_attn.v_proj.weight": f"layers.{layer_idx}.self_attn.v_proj.weight",
            f"layers.{layer_idx}.self_attn.o_proj.weight": f"layers.{layer_idx}.self_attn.o_proj.weight",

            # MLP
            f"layers.{layer_idx}.mlp.gate_proj.weight": f"layers.{layer_idx}.mlp.gate_proj.weight",
            f"layers.{layer_idx}.mlp.up_proj.weight": f"layers.{layer_idx}.mlp.up_proj.weight",
            f"layers.{layer_idx}.mlp.down_proj.weight": f"layers.{layer_idx}.mlp.down_proj.weight",

            # Layer norms
            f"layers.{layer_idx}.input_layernorm.weight": f"layers.{layer_idx}.input_layernorm.weight",
            f"layers.{layer_idx}.post_attention_layernorm.weight": f"layers.{layer_idx}.post_attention_layernorm.weight",
        }
        mapping.update(layer_mapping)

    return mapping


def copy_test_weights(test_model, fastllama_model):
    """Copy weights from test model to FastLLaMA model."""
    logger = logging.getLogger(__name__)

    test_state_dict = test_model.state_dict()
    fastllama_state_dict = fastllama_model.state_dict()

    # Create weight mapping
    weight_mapping = create_weight_mapping_for_test(fastllama_model.config.num_hidden_layers)

    # Copy weights
    copied_weights = []
    missing_weights = []
    size_mismatches = []

    for test_key, fastllama_key in weight_mapping.items():
        if test_key in test_state_dict and fastllama_key in fastllama_state_dict:
            test_weight = test_state_dict[test_key]
            fastllama_weight = fastllama_state_dict[fastllama_key]

            if test_weight.shape == fastllama_weight.shape:
                fastllama_state_dict[fastllama_key] = test_weight.clone()
                copied_weights.append(fastllama_key)
            else:
                size_mismatches.append({
                    'key': fastllama_key,
                    'test_shape': test_weight.shape,
                    'fastllama_shape': fastllama_weight.shape
                })
        else:
            if test_key not in test_state_dict:
                missing_weights.append(f"Test model missing: {test_key}")
            if fastllama_key not in fastllama_state_dict:
                missing_weights.append(f"FastLLaMA missing: {fastllama_key}")

    # Load the updated state dict
    missing_keys, unexpected_keys = fastllama_model.load_state_dict(fastllama_state_dict, strict=False)

    # Report results
    logger.info(f"Weight copying results:")
    logger.info(f"  - Successfully copied: {len(copied_weights)} weights")
    logger.info(f"  - Size mismatches: {len(size_mismatches)}")
    logger.info(f"  - Missing weights: {len(missing_weights)}")
    logger.info(f"  - Missing keys in FastLLaMA: {len(missing_keys)}")
    logger.info(f"  - Unexpected keys: {len(unexpected_keys)}")

    if size_mismatches:
        logger.warning("Size mismatches found:")
        for mismatch in size_mismatches[:5]:
            logger.warning(f"  {mismatch['key']}: {mismatch['test_shape']} -> {mismatch['fastllama_shape']}")

    return {
        'copied_weights': copied_weights,
        'size_mismatches': size_mismatches,
        'missing_weights': missing_weights,
        'missing_keys': missing_keys,
        'unexpected_keys': unexpected_keys
    }


def main():
    """Test weight copying functionality."""
    logger = setup_logging()
    logger.info("🧪 Testing weight copying from LLaMA-like model to FastLLaMA")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    try:
        # 1. Create test LLaMA model
        logger.info("🔧 Creating test LLaMA-like model...")
        test_model, test_config = create_test_llama_model()
        test_model.to(device)

        # 2. Create FastLLaMA config
        logger.info("⚙️ Creating FastLLaMA configuration...")
        fastllama_config = FastLLaMAConfig(
            hidden_size=test_config.hidden_size,
            intermediate_size=test_config.intermediate_size,
            num_attention_heads=test_config.num_attention_heads,
            num_key_value_heads=test_config.num_key_value_heads,
            num_hidden_layers=test_config.num_hidden_layers,
            vocab_size=test_config.vocab_size,
            max_position_embeddings=test_config.max_position_embeddings,
            rope_theta=test_config.rope_theta,
            rms_norm_eps=test_config.rms_norm_eps,

            # FastLLaMA features (disabled for testing)
            enable_context_compression=False,
            enable_early_exit=False,
            use_gradient_checkpointing=False,

            # Adjust early exit layers for small model
            early_exit_layers=[],  # Empty for testing
            confidence_threshold=0.8,
        )

        # 3. Create FastLLaMA model
        logger.info("🧠 Creating FastLLaMA model...")
        fastllama_model = FastLLaMAModel(fastllama_config)
        fastllama_model.to(device)

        logger.info(f"✅ Models created:")
        logger.info(f"  - Test model: {sum(p.numel() for p in test_model.parameters())/1e6:.1f}M parameters")
        logger.info(f"  - FastLLaMA model: {sum(p.numel() for p in fastllama_model.parameters())/1e6:.1f}M parameters")

        # 4. Copy weights
        logger.info("🔄 Copying weights...")
        copy_results = copy_test_weights(test_model, fastllama_model)

        # 5. Test forward pass
        logger.info("🧪 Testing forward pass...")
        batch_size, seq_len = 2, 10
        input_ids = torch.randint(0, test_config.vocab_size, (batch_size, seq_len)).to(device)

        with torch.no_grad():
            # Test original model
            test_output = test_model.lm_head(test_model.norm(test_model.embed_tokens(input_ids)))

            # Test FastLLaMA model
            fastllama_output = fastllama_model(input_ids)

        logger.info(f"✅ Forward pass successful:")
        logger.info(f"  - Test model output shape: {test_output.shape}")
        logger.info(f"  - FastLLaMA output shape: {fastllama_output['logits'].shape}")
        logger.info(f"  - FastLLaMA output keys: {list(fastllama_output.keys())}")

        # 6. Save test model
        output_dir = Path("./test_weight_copy_output")
        output_dir.mkdir(parents=True, exist_ok=True)

        torch.save(fastllama_model.state_dict(), output_dir / "pytorch_model.bin")
        fastllama_config.save_pretrained(output_dir)

        logger.info(f"✅ Test model saved to {output_dir}")

        return fastllama_model, copy_results

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    model, results = main()
    print("\n🎉 Weight copying test completed!")
    print(f"✅ Successfully copied {len(results['copied_weights'])} weights")
    print(f"⚠️  Size mismatches: {len(results['size_mismatches'])}")
    print(f"⚠️  Missing weights: {len(results['missing_weights'])}")
    print("🚀 Ready for real LLaMA model conversion!")
