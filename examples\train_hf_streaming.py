"""
Production Training Script for FastLLaMA with HuggingFace Streaming Datasets.

Features:
- HuggingFace datasets streaming
- Efficient data loading and preprocessing
- Multi-GPU training support
- Comprehensive logging and monitoring
- Checkpoint management
- Memory optimization
"""

import os
import sys
import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
import argparse
import logging
from datetime import datetime
import json
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.training import FastLLaMATrainer, TrainingArguments
from fastllama.data import create_dataloader, DataConfig
from fastllama.utils import MemoryOptimizer, MetricsCollector, get_memory_stats
from transformers import AutoTokenizer


def setup_logging(output_dir: str, rank: int = 0):
    """Setup comprehensive logging."""
    log_dir = Path(output_dir) / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)

    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"training_rank_{rank}_{timestamp}.log"

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout) if rank == 0 else logging.NullHandler()
        ]
    )

    return logging.getLogger(__name__)


def setup_distributed():
    """Setup distributed training."""
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        rank = int(os.environ['RANK'])
        world_size = int(os.environ['WORLD_SIZE'])
        local_rank = int(os.environ['LOCAL_RANK'])

        # Initialize process group
        dist.init_process_group(
            backend='nccl',
            init_method='env://',
            rank=rank,
            world_size=world_size
        )

        # Set device
        torch.cuda.set_device(local_rank)

        return rank, world_size, local_rank
    else:
        return 0, 1, -1


def create_model_and_config(args) -> tuple:
    """Create FastLLaMA model and configuration."""
    # Create configuration
    config = FastLLaMAConfig(
        # Model architecture
        hidden_size=args.hidden_size,
        intermediate_size=args.intermediate_size,
        num_attention_heads=args.num_attention_heads,
        num_key_value_heads=args.num_key_value_heads,
        num_hidden_layers=args.num_hidden_layers,
        vocab_size=args.vocab_size,
        max_position_embeddings=args.max_position_embeddings,

        # FastLLaMA optimizations
        enable_context_compression=args.enable_context_compression,
        enable_early_exit=args.enable_early_exit,
        use_gradient_checkpointing=args.use_gradient_checkpointing,
        use_mixed_precision=args.use_mixed_precision,
        kv_cache_quantization=args.kv_cache_quantization,
        parameter_sharing=args.parameter_sharing,

        # Hierarchical attention
        local_attention_window=args.local_attention_window,
        sparse_attention_stride=args.sparse_attention_stride,
        compression_ratio=args.compression_ratio,

        # Early exit configuration
        early_exit_layers=args.early_exit_layers,
        confidence_threshold=args.confidence_threshold,

        # Training optimizations
        rope_theta=args.rope_theta,
        rms_norm_eps=args.rms_norm_eps,
    )

    # Create model
    model = FastLLaMAModel(config)

    return model, config


def create_datasets_and_tokenizer(args, logger):
    """Create datasets and tokenizer."""
    logger.info(f"Loading dataset: {args.dataset_name}")

    # Create data configuration
    data_config = DataConfig(
        dataset_name=args.dataset_name,
        dataset_config=args.dataset_config,
        tokenizer_name=args.tokenizer_name,
        max_length=args.max_seq_length,
        batch_size=args.per_device_train_batch_size,
        streaming=args.streaming,
        text_column=args.text_column,
        num_workers=args.dataloader_num_workers,
        shuffle=True,
        drop_last=True,
        pin_memory=True,
    )

    # Create dataloaders
    try:
        train_dataloader, eval_dataloader, tokenizer = create_dataloader(
            dataset_name=args.dataset_name,
            dataset_config=args.dataset_config,
            tokenizer_name=args.tokenizer_name,
            max_length=args.max_seq_length,
            batch_size=args.per_device_train_batch_size,
            streaming=args.streaming,
            text_column=args.text_column,
            num_workers=args.dataloader_num_workers,
        )

        logger.info(f"Created dataloaders successfully")
        logger.info(f"Tokenizer vocab size: {tokenizer.vocab_size}")

        return train_dataloader, eval_dataloader, tokenizer

    except Exception as e:
        logger.error(f"Failed to create dataloaders: {e}")
        logger.info("Falling back to wikitext-2-raw-v1")

        # Fallback to a reliable dataset
        train_dataloader, eval_dataloader, tokenizer = create_dataloader(
            dataset_name="wikitext",
            dataset_config="wikitext-2-raw-v1",
            tokenizer_name=args.tokenizer_name,
            max_length=args.max_seq_length,
            batch_size=args.per_device_train_batch_size,
            streaming=args.streaming,
            num_workers=args.dataloader_num_workers,
        )

        return train_dataloader, eval_dataloader, tokenizer


def main():
    parser = argparse.ArgumentParser(description="Train FastLLaMA with HuggingFace Streaming Datasets")

    # Model configuration
    parser.add_argument("--hidden_size", type=int, default=1024)
    parser.add_argument("--intermediate_size", type=int, default=2048)
    parser.add_argument("--num_attention_heads", type=int, default=16)
    parser.add_argument("--num_key_value_heads", type=int, default=4)
    parser.add_argument("--num_hidden_layers", type=int, default=12)
    parser.add_argument("--vocab_size", type=int, default=32000)
    parser.add_argument("--max_position_embeddings", type=int, default=4096)

    # Dataset configuration
    parser.add_argument("--dataset_name", type=str, default="wikitext")
    parser.add_argument("--dataset_config", type=str, default="wikitext-2-raw-v1")
    parser.add_argument("--tokenizer_name", type=str, default="gpt2")
    parser.add_argument("--text_column", type=str, default="text")
    parser.add_argument("--streaming", action="store_true", default=True)
    parser.add_argument("--max_seq_length", type=int, default=1024)

    # Training configuration
    parser.add_argument("--output_dir", type=str, default="./fastllama_hf_output")
    parser.add_argument("--num_train_epochs", type=int, default=3)
    parser.add_argument("--per_device_train_batch_size", type=int, default=2)
    parser.add_argument("--per_device_eval_batch_size", type=int, default=2)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=4)
    parser.add_argument("--learning_rate", type=float, default=5e-5)
    parser.add_argument("--weight_decay", type=float, default=0.01)
    parser.add_argument("--warmup_steps", type=int, default=1000)
    parser.add_argument("--max_steps", type=int, default=-1)

    # FastLLaMA features
    parser.add_argument("--enable_context_compression", action="store_true", default=True)
    parser.add_argument("--enable_early_exit", action="store_true", default=True)
    parser.add_argument("--use_gradient_checkpointing", action="store_true", default=True)
    parser.add_argument("--use_mixed_precision", action="store_true", default=True)
    parser.add_argument("--kv_cache_quantization", action="store_true", default=False)
    parser.add_argument("--parameter_sharing", action="store_true", default=False)

    # Attention configuration
    parser.add_argument("--local_attention_window", type=int, default=512)
    parser.add_argument("--sparse_attention_stride", type=int, default=8)
    parser.add_argument("--compression_ratio", type=int, default=16)

    # Early exit configuration
    parser.add_argument("--early_exit_layers", nargs="+", type=int, default=[6, 9])
    parser.add_argument("--confidence_threshold", type=float, default=0.7)

    # Model parameters
    parser.add_argument("--rope_theta", type=float, default=10000.0)
    parser.add_argument("--rms_norm_eps", type=float, default=1e-6)

    # Training strategy
    parser.add_argument("--foundation_phase_ratio", type=float, default=0.7)
    parser.add_argument("--long_context_phase_ratio", type=float, default=0.2)
    parser.add_argument("--efficiency_phase_ratio", type=float, default=0.1)

    # Logging and evaluation
    parser.add_argument("--eval_steps", type=int, default=500)
    parser.add_argument("--save_steps", type=int, default=1000)
    parser.add_argument("--logging_steps", type=int, default=50)
    parser.add_argument("--eval_strategy", type=str, default="steps")
    parser.add_argument("--save_strategy", type=str, default="steps")

    # Hardware settings
    parser.add_argument("--dataloader_num_workers", type=int, default=4)
    parser.add_argument("--fp16", action="store_true", default=False)
    parser.add_argument("--bf16", action="store_true", default=False)

    # Checkpointing
    parser.add_argument("--resume_from_checkpoint", type=str, default=None)
    parser.add_argument("--save_total_limit", type=int, default=3)

    # Monitoring
    parser.add_argument("--report_to", type=str, default=None, help="wandb, tensorboard, etc.")
    parser.add_argument("--run_name", type=str, default=None)

    args = parser.parse_args()

    # Setup distributed training
    rank, world_size, local_rank = setup_distributed()

    # Setup logging
    logger = setup_logging(args.output_dir, rank)

    if rank == 0:
        logger.info("Starting FastLLaMA training with HuggingFace datasets")
        logger.info(f"Arguments: {json.dumps(vars(args), indent=2)}")
        logger.info(f"Distributed training: rank={rank}, world_size={world_size}, local_rank={local_rank}")

    # Set device
    device = torch.device(f"cuda:{local_rank}" if local_rank >= 0 else "cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # Create datasets and tokenizer
    train_dataloader, eval_dataloader, tokenizer = create_datasets_and_tokenizer(args, logger)

    # Update vocab size based on tokenizer
    args.vocab_size = tokenizer.vocab_size

    # Create model and configuration
    model, config = create_model_and_config(args)
    model.to(device)

    if rank == 0:
        num_params = sum(p.numel() for p in model.parameters())
        logger.info(f"Created FastLLaMA model with {num_params/1e6:.1f}M parameters")

    # Setup memory optimizer
    memory_optimizer = MemoryOptimizer(model, config)
    memory_optimizer.optimize_for_training()

    # Setup distributed model
    if world_size > 1:
        model = DDP(model, device_ids=[local_rank], output_device=local_rank)

    # Create training arguments
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        num_train_epochs=args.num_train_epochs,
        per_device_train_batch_size=args.per_device_train_batch_size,
        per_device_eval_batch_size=args.per_device_eval_batch_size,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        warmup_steps=args.warmup_steps,

        # Memory optimizations
        use_mixed_precision=args.use_mixed_precision,
        gradient_checkpointing=args.use_gradient_checkpointing,
        max_grad_norm=1.0,

        # Sequence length scheduling
        initial_seq_length=512,
        max_seq_length=args.max_seq_length,
        seq_length_warmup_steps=2000,

        # Evaluation and logging
        eval_steps=args.eval_steps,
        save_steps=args.save_steps,
        logging_steps=args.logging_steps,

        # Phase-based training
        foundation_phase_ratio=args.foundation_phase_ratio,
        long_context_phase_ratio=args.long_context_phase_ratio,
        efficiency_phase_ratio=args.efficiency_phase_ratio,

        # Hardware settings
        dataloader_num_workers=args.dataloader_num_workers,
        local_rank=local_rank,

        # Early exit training
        early_exit_loss_weight=0.1,
        confidence_loss_weight=0.05,
    )

    # Create trainer
    trainer = FastLLaMATrainer(
        model=model,
        config=config,
        args=training_args,
        train_dataset=train_dataloader,
        eval_dataset=eval_dataloader,
        tokenizer=tokenizer,
    )

    # Setup metrics collector
    metrics_collector = MetricsCollector(config)

    if rank == 0:
        logger.info("Starting training...")
        initial_memory = get_memory_stats()
        logger.info(f"Initial memory usage: {initial_memory}")

    try:
        # Resume from checkpoint if specified
        if args.resume_from_checkpoint:
            logger.info(f"Resuming from checkpoint: {args.resume_from_checkpoint}")
            trainer.load_checkpoint(args.resume_from_checkpoint)

        # Train the model
        training_metrics = trainer.train()

        if rank == 0:
            logger.info("Training completed successfully!")
            logger.info(f"Final training metrics: {training_metrics}")

            # Save final metrics
            metrics_collector.save_metrics(args.output_dir)

            # Print memory usage summary
            final_memory = get_memory_stats()
            logger.info(f"Final memory usage: {final_memory}")

            # Save training summary
            summary = {
                "args": vars(args),
                "model_config": config.to_dict(),
                "training_metrics": training_metrics,
                "memory_stats": {
                    "initial": initial_memory,
                    "final": final_memory
                }
            }

            summary_path = Path(args.output_dir) / "training_summary.json"
            with open(summary_path, 'w') as f:
                json.dump(summary, f, indent=2)

            logger.info(f"Training summary saved to {summary_path}")

    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise

    finally:
        # Cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        if world_size > 1:
            dist.destroy_process_group()


def get_dataset_configs():
    """Get predefined dataset configurations."""
    return {
        "wikitext": {
            "dataset_name": "wikitext",
            "dataset_config": "wikitext-2-raw-v1",
            "text_column": "text",
            "tokenizer_name": "gpt2"
        },
        "openwebtext": {
            "dataset_name": "openwebtext",
            "dataset_config": None,
            "text_column": "text",
            "tokenizer_name": "gpt2"
        },
        "c4": {
            "dataset_name": "c4",
            "dataset_config": "en",
            "text_column": "text",
            "tokenizer_name": "t5-base"
        },
        "pile": {
            "dataset_name": "EleutherAI/pile",
            "dataset_config": None,
            "text_column": "text",
            "tokenizer_name": "gpt2"
        },
        "bookcorpus": {
            "dataset_name": "bookcorpus",
            "dataset_config": None,
            "text_column": "text",
            "tokenizer_name": "bert-base-uncased"
        },
        "oscar": {
            "dataset_name": "oscar",
            "dataset_config": "unshuffled_deduplicated_en",
            "text_column": "text",
            "tokenizer_name": "gpt2"
        }
    }


if __name__ == "__main__":
    main()
