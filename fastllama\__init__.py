"""
FastLLaMA: Optimized LLaMA Architecture for Production

A production-ready implementation of FastLLaMA with hierarchical attention,
context compression, and memory optimizations for efficient long-context processing.
"""

__version__ = "1.0.0"
__author__ = "FastLLaMA Team"

from .config import FastLLaMAConfig
from .models import FastLLaMAModel
from .training import FastLLaMATrainer
from .inference import FastLLaMAInferenceEngine

__all__ = [
    "FastLLaMAConfig",
    "FastLLaMAModel", 
    "FastLLaMATrainer",
    "FastLLaMAInferenceEngine",
]
