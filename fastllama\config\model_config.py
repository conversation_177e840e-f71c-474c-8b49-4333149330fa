"""
FastLLaMA Model Configuration

Defines the configuration class for FastLLaMA models with all architectural parameters
and optimization settings.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
import json
import os
import torch


@dataclass
class FastLLaMAConfig:
    """Configuration class for FastLLaMA models."""
    # Global device configuration
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Model Architecture
    hidden_size: int = 4096
    intermediate_size: int = 11008
    num_attention_heads: int = 32
    num_key_value_heads: int = 8  # For GQA
    num_hidden_layers: int = 32
    vocab_size: int = 32000
    max_position_embeddings: int = 1048576  # 1M tokens

    # RoPE Configuration
    rope_theta: float = 10000.0
    rope_scaling: Optional[Dict[str, Any]] = None

    # Activation and Normalization
    hidden_act: str = "silu"
    rms_norm_eps: float = 1e-6

    # Hierarchical Attention Configuration
    local_attention_window: int = 512
    sparse_attention_stride: int = 8
    compression_ratio: int = 20

    # Layer-wise Attention Patterns
    local_layers: List[int] = field(default_factory=lambda: list(range(1, 9)))
    sparse_layers: List[int] = field(default_factory=lambda: list(range(9, 17)))
    hierarchical_layers: List[int] = field(default_factory=lambda: list(range(17, 25)))
    full_attention_layers: List[int] = field(default_factory=lambda: list(range(25, 33)))

    # Dynamic Layer Scaling
    early_exit_layers: List[int] = field(default_factory=lambda: [12, 18, 24])
    confidence_threshold: float = 0.8
    enable_early_exit: bool = True

    # Context Compression
    compression_encoder_layers: int = 4
    enable_context_compression: bool = True
    progressive_compression: bool = True

    # Memory Optimizations
    use_gradient_checkpointing: bool = True
    gradient_checkpointing_ratio: float = 0.5
    use_mixed_precision: bool = True
    kv_cache_quantization: bool = True
    parameter_sharing: bool = True

    # Training Configuration
    initializer_range: float = 0.02
    use_cache: bool = True
    pad_token_id: Optional[int] = None
    bos_token_id: int = 1
    eos_token_id: int = 2

    # Performance Optimizations
    use_flash_attention: bool = True
    use_kernel_fusion: bool = True
    enable_speculative_decoding: bool = True

    # Batch Processing
    max_batch_size: int = 32
    dynamic_batching: bool = True
    memory_aware_batching: bool = True

    # Additional attributes for compatibility
    output_attentions: bool = False
    output_hidden_states: bool = False
    use_return_dict: bool = True

    def __post_init__(self):
        """Validate and adjust configuration after initialization."""
        # Ensure GQA configuration is valid
        if self.num_key_value_heads > self.num_attention_heads:
            raise ValueError("num_key_value_heads cannot be greater than num_attention_heads")

        # Ensure early exit layers are valid
        for layer in self.early_exit_layers:
            if layer >= self.num_hidden_layers:
                raise ValueError(f"Early exit layer {layer} exceeds num_hidden_layers {self.num_hidden_layers}")

        # Set default rope scaling if not provided
        if self.rope_scaling is None:
            self.rope_scaling = {
                "type": "linear",
                "factor": max(1.0, self.max_position_embeddings / 4096)
            }

    @classmethod
    def from_pretrained(cls, model_name_or_path: str) -> "FastLLaMAConfig":
        """Load configuration from a pretrained model."""
        config_path = os.path.join(model_name_or_path, "config.json")
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config_dict = json.load(f)
            return cls(**config_dict)
        else:
            raise FileNotFoundError(f"Configuration file not found at {config_path}")

    def save_pretrained(self, save_directory: str):
        """Save configuration to a directory."""
        os.makedirs(save_directory, exist_ok=True)
        config_path = os.path.join(save_directory, "config.json")

        # Convert to dictionary and handle non-serializable types
        config_dict = self.to_dict()

        with open(config_path, 'w') as f:
            json.dump(config_dict, f, indent=2)

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            key: getattr(self, key) for key in self.__dataclass_fields__.keys()
        }

    @property
    def head_dim(self) -> int:
        """Calculate attention head dimension."""
        return self.hidden_size // self.num_attention_heads

    @property
    def num_groups(self) -> int:
        """Calculate number of groups for GQA."""
        return self.num_attention_heads // self.num_key_value_heads


# Predefined configurations for different model sizes
FASTLLAMA_7B_CONFIG = FastLLaMAConfig(
    hidden_size=4096,
    intermediate_size=11008,
    num_attention_heads=32,
    num_key_value_heads=8,
    num_hidden_layers=32,
    vocab_size=32000,
)

FASTLLAMA_13B_CONFIG = FastLLaMAConfig(
    hidden_size=5120,
    intermediate_size=13824,
    num_attention_heads=40,
    num_key_value_heads=10,
    num_hidden_layers=40,
    vocab_size=32000,
)

FASTLLAMA_30B_CONFIG = FastLLaMAConfig(
    hidden_size=6656,
    intermediate_size=17920,
    num_attention_heads=52,
    num_key_value_heads=13,
    num_hidden_layers=60,
    vocab_size=32000,
)

FASTLLAMA_65B_CONFIG = FastLLaMAConfig(
    hidden_size=8192,
    intermediate_size=22016,
    num_attention_heads=64,
    num_key_value_heads=16,
    num_hidden_layers=80,
    vocab_size=32000,
)
