"""
HuggingFace Dataset Integration for FastLLaMA.

Provides streaming dataset support, efficient data loading, and preprocessing
for large-scale language model training.
"""

import torch
from torch.utils.data import DataLoader, IterableDataset
from datasets import load_dataset, Dataset, IterableDataset as HFIterableDataset
from transformers import AutoTokenizer
from typing import Dict, List, Optional, Union, Iterator, Any
import logging
import random
from dataclasses import dataclass


logger = logging.getLogger(__name__)


@dataclass
class DataConfig:
    """Configuration for data loading and preprocessing."""

    # Dataset configuration
    dataset_name: str = "openwebtext"
    dataset_config: Optional[str] = None
    dataset_split: str = "train"
    streaming: bool = True

    # Tokenization
    tokenizer_name: str = "meta-llama/Llama-2-7b-hf"
    max_length: int = 2048
    truncation: bool = True
    padding: str = "max_length"

    # Data loading
    batch_size: int = 4
    num_workers: int = 4
    shuffle: bool = True
    drop_last: bool = True

    # Preprocessing
    text_column: str = "text"
    remove_columns: Optional[List[str]] = None
    filter_empty: bool = True
    min_length: int = 10

    # Memory optimization
    pin_memory: bool = True
    prefetch_factor: int = 2


class StreamingTextDataset(IterableDataset):
    """
    Streaming dataset for large-scale text data using HuggingFace datasets.

    Supports:
    - Streaming from HuggingFace Hub
    - Dynamic tokenization
    - Memory-efficient processing
    - Sequence length adaptation
    """

    def __init__(
        self,
        config: DataConfig,
        tokenizer: Optional[Any] = None,
        split: str = "train",
        seed: int = 42
    ):
        self.config = config
        self.split = split
        self.seed = seed

        # Initialize tokenizer
        if tokenizer is None:
            self.tokenizer = AutoTokenizer.from_pretrained(config.tokenizer_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
        else:
            self.tokenizer = tokenizer

        # Load dataset
        self.dataset = self._load_dataset()

        logger.info(f"Initialized StreamingTextDataset with {config.dataset_name}")

    def _load_dataset(self) -> Union[Dataset, HFIterableDataset]:
        """Load dataset from HuggingFace Hub."""
        try:
            dataset = load_dataset(
                self.config.dataset_name,
                self.config.dataset_config,
                split=self.split,
                streaming=self.config.streaming,
                trust_remote_code=True
            )

            if self.config.streaming:
                # Shuffle streaming dataset
                if self.config.shuffle:
                    dataset = dataset.shuffle(seed=self.seed, buffer_size=10000)

            logger.info(f"Loaded dataset: {self.config.dataset_name}")
            return dataset

        except Exception as e:
            logger.error(f"Failed to load dataset {self.config.dataset_name}: {e}")
            # Fallback to a smaller dataset
            logger.info("Falling back to wikitext-2-raw-v1")
            return load_dataset("wikitext", "wikitext-2-raw-v1", split=self.split, streaming=self.config.streaming)

    def _preprocess_function(self, examples: Dict[str, List]) -> Dict[str, List]:
        """Preprocess text examples."""
        texts = examples[self.config.text_column]

        # Filter empty or too short texts
        if self.config.filter_empty:
            filtered_texts = []
            for text in texts:
                if text and isinstance(text, str) and len(text.strip()) >= self.config.min_length:
                    filtered_texts.append(text)
            texts = filtered_texts

        if not texts:
            return {"input_ids": [], "attention_mask": [], "labels": []}

        # Tokenize
        tokenized = self.tokenizer(
            texts,
            truncation=self.config.truncation,
            padding=self.config.padding,
            max_length=self.config.max_length,
            return_tensors="pt"
        )

        # Create labels (same as input_ids for language modeling)
        tokenized["labels"] = tokenized["input_ids"].clone()

        # Set padding tokens to -100 so they're ignored in loss calculation
        if self.tokenizer.pad_token_id is not None:
            tokenized["labels"][tokenized["input_ids"] == self.tokenizer.pad_token_id] = -100

        return tokenized

    def __iter__(self) -> Iterator[Dict[str, torch.Tensor]]:
        """Iterate over the dataset."""
        if self.config.streaming:
            # For streaming datasets
            for example in self.dataset:
                try:
                    # Process single example
                    processed = self._preprocess_function({self.config.text_column: [example[self.config.text_column]]})

                    if len(processed["input_ids"]) > 0:
                        yield {
                            "input_ids": processed["input_ids"][0],
                            "attention_mask": processed["attention_mask"][0],
                            "labels": processed["labels"][0]
                        }
                except Exception as e:
                    logger.warning(f"Skipping example due to error: {e}")
                    continue
        else:
            # For non-streaming datasets
            for i in range(len(self.dataset)):
                try:
                    example = self.dataset[i]
                    processed = self._preprocess_function({self.config.text_column: [example[self.config.text_column]]})

                    if processed["input_ids"]:
                        yield {
                            "input_ids": processed["input_ids"][0],
                            "attention_mask": processed["attention_mask"][0],
                            "labels": processed["labels"][0]
                        }
                except Exception as e:
                    logger.warning(f"Skipping example {i} due to error: {e}")
                    continue


class FastLLaMADataLoader:
    """
    Enhanced DataLoader for FastLLaMA with dynamic batching and sequence length adaptation.
    """

    def __init__(self, config: DataConfig, tokenizer: Optional[Any] = None):
        self.config = config
        self.tokenizer = tokenizer

    def create_train_dataloader(self, seed: int = 42) -> DataLoader:
        """Create training dataloader."""
        dataset = StreamingTextDataset(
            config=self.config,
            tokenizer=self.tokenizer,
            split="train",
            seed=seed
        )

        return DataLoader(
            dataset,
            batch_size=self.config.batch_size,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory,
            drop_last=self.config.drop_last,
            prefetch_factor=self.config.prefetch_factor,
            collate_fn=self._collate_fn
        )

    def create_eval_dataloader(self, seed: int = 42) -> DataLoader:
        """Create evaluation dataloader."""
        # Use validation split if available, otherwise use a subset of train
        try:
            dataset = StreamingTextDataset(
                config=self.config,
                tokenizer=self.tokenizer,
                split="validation",
                seed=seed
            )
        except:
            # Fallback to train split
            dataset = StreamingTextDataset(
                config=self.config,
                tokenizer=self.tokenizer,
                split="train",
                seed=seed + 1000  # Different seed for eval
            )

        eval_config = DataConfig(**self.config.__dict__)
        eval_config.shuffle = False  # Don't shuffle eval data

        return DataLoader(
            dataset,
            batch_size=self.config.batch_size,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory,
            drop_last=False,  # Keep all eval data
            collate_fn=self._collate_fn
        )

    def _collate_fn(self, batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """Custom collate function for dynamic batching."""
        if not batch:
            return {}

        # Find the maximum length in the batch
        max_length = max(item["input_ids"].size(0) for item in batch)

        # Pad all sequences to the maximum length
        input_ids_list = []
        attention_mask_list = []
        labels_list = []

        pad_token_id = self.tokenizer.pad_token_id if self.tokenizer.pad_token_id is not None else self.tokenizer.eos_token_id

        for item in batch:
            seq_len = item["input_ids"].size(0)
            pad_length = max_length - seq_len

            if pad_length > 0:
                # Pad input_ids
                padded_input_ids = torch.cat([
                    item["input_ids"],
                    torch.full((pad_length,), pad_token_id, dtype=item["input_ids"].dtype)
                ])

                # Pad attention_mask
                padded_attention_mask = torch.cat([
                    item["attention_mask"],
                    torch.zeros(pad_length, dtype=item["attention_mask"].dtype)
                ])

                # Pad labels (with -100 for padding tokens)
                padded_labels = torch.cat([
                    item["labels"],
                    torch.full((pad_length,), -100, dtype=item["labels"].dtype)
                ])
            else:
                padded_input_ids = item["input_ids"]
                padded_attention_mask = item["attention_mask"]
                padded_labels = item["labels"]

            input_ids_list.append(padded_input_ids)
            attention_mask_list.append(padded_attention_mask)
            labels_list.append(padded_labels)

        # Stack tensors
        input_ids = torch.stack(input_ids_list)
        attention_mask = torch.stack(attention_mask_list)
        labels = torch.stack(labels_list)

        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": labels
        }


def create_dataloader(
    dataset_name: str = "wikitext",
    dataset_config: str = "wikitext-2-raw-v1",
    tokenizer_name: str = "gpt2",
    max_length: int = 1024,
    batch_size: int = 4,
    streaming: bool = True,
    **kwargs
) -> tuple:
    """
    Convenience function to create train and eval dataloaders.

    Args:
        dataset_name: HuggingFace dataset name
        dataset_config: Dataset configuration
        tokenizer_name: Tokenizer name
        max_length: Maximum sequence length
        batch_size: Batch size
        streaming: Whether to use streaming
        **kwargs: Additional configuration options

    Returns:
        Tuple of (train_dataloader, eval_dataloader, tokenizer)
    """
    # Create configuration
    config = DataConfig(
        dataset_name=dataset_name,
        dataset_config=dataset_config,
        tokenizer_name=tokenizer_name,
        max_length=max_length,
        batch_size=batch_size,
        streaming=streaming,
        **kwargs
    )

    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Create dataloader
    dataloader = FastLLaMADataLoader(config, tokenizer)

    # Create train and eval dataloaders
    train_dataloader = dataloader.create_train_dataloader()
    eval_dataloader = dataloader.create_eval_dataloader()

    return train_dataloader, eval_dataloader, tokenizer


def get_dataset(dataset_name: str, dataset_config: str = None, **kwargs) -> Dict[str, Any]:
    """Get dataset information and sample data."""
    try:
        # Load a small sample to inspect the dataset
        if dataset_config:
            dataset = load_dataset(dataset_name, dataset_config, split="train", streaming=True, **kwargs)
        else:
            dataset = load_dataset(dataset_name, split="train", streaming=True, **kwargs)

        # Get first few examples
        examples = []
        for i, example in enumerate(dataset):
            if i >= 3:
                break
            examples.append(example)

        return {
            "dataset_name": dataset_name,
            "features": dataset.features if hasattr(dataset, 'features') else None,
            "sample_examples": examples,
            "text_columns": [key for key in examples[0].keys() if isinstance(examples[0][key], str)]
        }

    except Exception as e:
        logger.error(f"Failed to inspect dataset {dataset_name}: {e}")
        return {"error": str(e)}
