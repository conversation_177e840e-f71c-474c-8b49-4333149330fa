"""
Text preprocessing utilities for FastLLaMA training.

Provides tokenization, text cleaning, and data collation functionality.
"""

import torch
import re
import html
from typing import Dict, List, Optional, Any, Union
from transformers import AutoTokenizer, PreTrainedTokenizer
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class PreprocessingConfig:
    """Configuration for text preprocessing."""
    
    # Text cleaning
    clean_html: bool = True
    clean_urls: bool = True
    clean_emails: bool = True
    normalize_whitespace: bool = True
    remove_empty_lines: bool = True
    
    # Tokenization
    add_special_tokens: bool = True
    return_attention_mask: bool = True
    return_token_type_ids: bool = False
    
    # Sequence processing
    concatenate_sequences: bool = True
    sequence_separator: str = "\n\n"
    
    # Quality filtering
    min_chars: int = 50
    max_chars: int = 1000000
    min_words: int = 10
    filter_duplicates: bool = True


class TextPreprocessor:
    """Advanced text preprocessing for language model training."""
    
    def __init__(self, config: PreprocessingConfig = None):
        self.config = config or PreprocessingConfig()
        
        # Compile regex patterns for efficiency
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile regex patterns for text cleaning."""
        self.url_pattern = re.compile(
            r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        )
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.whitespace_pattern = re.compile(r'\s+')
        self.empty_line_pattern = re.compile(r'\n\s*\n')
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text."""
        if not text or not isinstance(text, str):
            return ""
        
        # HTML decoding
        if self.config.clean_html:
            text = html.unescape(text)
            text = re.sub(r'<[^>]+>', '', text)  # Remove HTML tags
        
        # Remove URLs
        if self.config.clean_urls:
            text = self.url_pattern.sub('[URL]', text)
        
        # Remove emails
        if self.config.clean_emails:
            text = self.email_pattern.sub('[EMAIL]', text)
        
        # Normalize whitespace
        if self.config.normalize_whitespace:
            text = self.whitespace_pattern.sub(' ', text)
        
        # Remove excessive empty lines
        if self.config.remove_empty_lines:
            text = self.empty_line_pattern.sub('\n\n', text)
        
        return text.strip()
    
    def filter_text(self, text: str) -> bool:
        """Filter text based on quality criteria."""
        if not text:
            return False
        
        # Character count filter
        char_count = len(text)
        if char_count < self.config.min_chars or char_count > self.config.max_chars:
            return False
        
        # Word count filter
        word_count = len(text.split())
        if word_count < self.config.min_words:
            return False
        
        # Additional quality checks
        if self._is_low_quality(text):
            return False
        
        return True
    
    def _is_low_quality(self, text: str) -> bool:
        """Check if text is low quality."""
        # Too many repeated characters
        if re.search(r'(.)\1{10,}', text):
            return True
        
        # Too many special characters
        special_char_ratio = len(re.findall(r'[^a-zA-Z0-9\s]', text)) / len(text)
        if special_char_ratio > 0.3:
            return True
        
        # Too many uppercase letters
        upper_ratio = len(re.findall(r'[A-Z]', text)) / len(text)
        if upper_ratio > 0.5:
            return True
        
        return False
    
    def process_batch(self, texts: List[str]) -> List[str]:
        """Process a batch of texts."""
        processed_texts = []
        
        for text in texts:
            cleaned_text = self.clean_text(text)
            if self.filter_text(cleaned_text):
                processed_texts.append(cleaned_text)
        
        return processed_texts


class TokenizerWrapper:
    """Enhanced tokenizer wrapper with FastLLaMA-specific optimizations."""
    
    def __init__(
        self,
        tokenizer_name: str,
        max_length: int = 2048,
        padding: str = "max_length",
        truncation: bool = True
    ):
        self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        self.max_length = max_length
        self.padding = padding
        self.truncation = truncation
        
        # Set pad token if not available
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        logger.info(f"Initialized tokenizer: {tokenizer_name}")
        logger.info(f"Vocab size: {self.tokenizer.vocab_size}")
        logger.info(f"Max length: {max_length}")
    
    def tokenize_texts(
        self,
        texts: List[str],
        return_tensors: str = "pt"
    ) -> Dict[str, torch.Tensor]:
        """Tokenize a list of texts."""
        return self.tokenizer(
            texts,
            max_length=self.max_length,
            padding=self.padding,
            truncation=self.truncation,
            return_tensors=return_tensors,
            return_attention_mask=True
        )
    
    def tokenize_single(self, text: str) -> Dict[str, torch.Tensor]:
        """Tokenize a single text."""
        return self.tokenize_texts([text])
    
    def decode_tokens(self, token_ids: torch.Tensor, skip_special_tokens: bool = True) -> List[str]:
        """Decode token IDs back to text."""
        if token_ids.dim() == 1:
            token_ids = token_ids.unsqueeze(0)
        
        return self.tokenizer.batch_decode(token_ids, skip_special_tokens=skip_special_tokens)
    
    def get_vocab_size(self) -> int:
        """Get vocabulary size."""
        return self.tokenizer.vocab_size
    
    def get_special_tokens(self) -> Dict[str, int]:
        """Get special token IDs."""
        return {
            "pad_token_id": self.tokenizer.pad_token_id,
            "eos_token_id": self.tokenizer.eos_token_id,
            "bos_token_id": getattr(self.tokenizer, 'bos_token_id', None),
            "unk_token_id": getattr(self.tokenizer, 'unk_token_id', None),
        }


class DataCollator:
    """
    Data collator for FastLLaMA with dynamic sequence length and memory optimization.
    """
    
    def __init__(
        self,
        tokenizer: PreTrainedTokenizer,
        max_length: int = 2048,
        pad_to_multiple_of: Optional[int] = None,
        return_tensors: str = "pt"
    ):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.pad_to_multiple_of = pad_to_multiple_of
        self.return_tensors = return_tensors
    
    def __call__(self, features: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """Collate a batch of features."""
        if not features:
            return {}
        
        # Extract keys from first feature
        keys = features[0].keys()
        
        # Initialize batch dictionary
        batch = {}
        
        for key in keys:
            if key in ["input_ids", "attention_mask", "labels"]:
                # Handle tensor data
                values = [feature[key] for feature in features]
                
                if isinstance(values[0], torch.Tensor):
                    # Stack tensors
                    batch[key] = torch.stack(values)
                else:
                    # Convert to tensors and stack
                    batch[key] = torch.tensor(values)
            else:
                # Handle other data types
                batch[key] = [feature[key] for feature in features]
        
        # Dynamic padding to the longest sequence in the batch
        if "input_ids" in batch:
            batch = self._pad_batch(batch)
        
        return batch
    
    def _pad_batch(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Pad batch to the longest sequence."""
        input_ids = batch["input_ids"]
        batch_size, current_length = input_ids.shape
        
        # Find the longest sequence in the batch
        max_length_in_batch = current_length
        
        # Optionally pad to multiple of specified value
        if self.pad_to_multiple_of:
            max_length_in_batch = (
                (max_length_in_batch + self.pad_to_multiple_of - 1) 
                // self.pad_to_multiple_of 
                * self.pad_to_multiple_of
            )
        
        # Ensure we don't exceed the maximum length
        max_length_in_batch = min(max_length_in_batch, self.max_length)
        
        # Pad if necessary
        if max_length_in_batch > current_length:
            pad_length = max_length_in_batch - current_length
            
            # Pad input_ids
            if "input_ids" in batch:
                pad_token_id = self.tokenizer.pad_token_id or self.tokenizer.eos_token_id
                padding = torch.full((batch_size, pad_length), pad_token_id, dtype=input_ids.dtype)
                batch["input_ids"] = torch.cat([input_ids, padding], dim=1)
            
            # Pad attention_mask
            if "attention_mask" in batch:
                padding = torch.zeros((batch_size, pad_length), dtype=batch["attention_mask"].dtype)
                batch["attention_mask"] = torch.cat([batch["attention_mask"], padding], dim=1)
            
            # Pad labels
            if "labels" in batch:
                padding = torch.full((batch_size, pad_length), -100, dtype=batch["labels"].dtype)
                batch["labels"] = torch.cat([batch["labels"], padding], dim=1)
        
        return batch


def create_preprocessing_pipeline(
    tokenizer_name: str,
    max_length: int = 2048,
    preprocessing_config: Optional[PreprocessingConfig] = None
) -> tuple:
    """
    Create a complete preprocessing pipeline.
    
    Returns:
        Tuple of (text_preprocessor, tokenizer_wrapper, data_collator)
    """
    # Initialize components
    preprocessing_config = preprocessing_config or PreprocessingConfig()
    text_preprocessor = TextPreprocessor(preprocessing_config)
    tokenizer_wrapper = TokenizerWrapper(tokenizer_name, max_length)
    data_collator = DataCollator(tokenizer_wrapper.tokenizer, max_length)
    
    return text_preprocessor, tokenizer_wrapper, data_collator
