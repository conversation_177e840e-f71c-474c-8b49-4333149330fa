"""
FastLLaMA Inference Engine.

Optimized inference engine with:
- Dynamic batching
- KV cache optimization
- Early exit support
- Speculative decoding
- Memory-efficient generation
"""

import torch
import torch.nn.functional as F
from typing import List, Dict, Optional, Union, Tuple, Any
import time
from dataclasses import dataclass

from ..config import FastLLaMAConfig
from ..models import FastLLaMAModel
from .speculative import SpeculativeDecoder


@dataclass
class GenerationConfig:
    """Configuration for text generation."""
    max_length: int = 2048
    max_new_tokens: Optional[int] = None
    min_length: int = 0
    do_sample: bool = True
    temperature: float = 1.0
    top_k: int = 50
    top_p: float = 0.9
    repetition_penalty: float = 1.0
    length_penalty: float = 1.0
    num_beams: int = 1
    early_stopping: bool = False
    pad_token_id: Optional[int] = None
    eos_token_id: Optional[int] = None
    use_cache: bool = True
    enable_early_exit: bool = True
    confidence_threshold: float = 0.8


@dataclass
class GenerationOutput:
    """Output from text generation."""
    sequences: torch.Tensor
    scores: Optional[torch.Tensor] = None
    attentions: Optional[Tuple[torch.Tensor]] = None
    hidden_states: Optional[Tuple[torch.Tensor]] = None
    early_exit_info: Optional[Dict[str, Any]] = None
    generation_stats: Optional[Dict[str, Any]] = None


class FastLLaMAInferenceEngine:
    """
    Optimized inference engine for FastLLaMA models.
    
    Features:
    - Dynamic batching for variable sequence lengths
    - KV cache optimization and quantization
    - Early exit support for faster inference
    - Speculative decoding integration
    - Memory-efficient generation strategies
    """
    
    def __init__(
        self,
        model: FastLLaMAModel,
        config: FastLLaMAConfig,
        device: Optional[torch.device] = None,
        enable_speculative_decoding: bool = True,
    ):
        self.model = model
        self.config = config
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Move model to device and set to eval mode
        self.model.to(self.device)
        self.model.eval()
        
        # Initialize speculative decoder
        if enable_speculative_decoding and config.enable_speculative_decoding:
            self.speculative_decoder = SpeculativeDecoder(model, config)
        else:
            self.speculative_decoder = None
        
        # KV cache management
        self.kv_cache = {}
        self.cache_size_limit = 1024 * 1024 * 1024  # 1GB limit
        
        # Performance tracking
        self.generation_stats = {
            "total_tokens": 0,
            "total_time": 0.0,
            "cache_hits": 0,
            "early_exits": 0,
            "speculative_accepts": 0,
        }
    
    @torch.no_grad()
    def generate(
        self,
        input_ids: torch.Tensor,
        generation_config: Optional[GenerationConfig] = None,
        attention_mask: Optional[torch.Tensor] = None,
        **kwargs
    ) -> GenerationOutput:
        """
        Generate text using the FastLLaMA model.
        
        Args:
            input_ids: Input token IDs [batch_size, seq_len]
            generation_config: Generation configuration
            attention_mask: Attention mask [batch_size, seq_len]
            **kwargs: Additional generation parameters
            
        Returns:
            GenerationOutput with generated sequences and metadata
        """
        # Setup generation config
        if generation_config is None:
            generation_config = GenerationConfig()
        
        # Override config with kwargs
        for key, value in kwargs.items():
            if hasattr(generation_config, key):
                setattr(generation_config, key, value)
        
        # Validate inputs
        batch_size, input_length = input_ids.shape
        input_ids = input_ids.to(self.device)
        
        if attention_mask is not None:
            attention_mask = attention_mask.to(self.device)
        else:
            attention_mask = torch.ones_like(input_ids)
        
        # Determine generation length
        if generation_config.max_new_tokens is not None:
            max_length = input_length + generation_config.max_new_tokens
        else:
            max_length = generation_config.max_length
        
        # Choose generation strategy
        if generation_config.num_beams > 1:
            return self._beam_search_generate(input_ids, attention_mask, generation_config, max_length)
        elif self.speculative_decoder is not None and generation_config.do_sample:
            return self._speculative_generate(input_ids, attention_mask, generation_config, max_length)
        else:
            return self._greedy_generate(input_ids, attention_mask, generation_config, max_length)
    
    def _greedy_generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor,
        generation_config: GenerationConfig,
        max_length: int
    ) -> GenerationOutput:
        """Greedy generation with early exit support."""
        batch_size, input_length = input_ids.shape
        generated_tokens = input_ids.clone()
        past_key_values = None
        early_exit_info = {"exits_used": [], "confidence_scores": []}
        
        start_time = time.time()
        
        for step in range(input_length, max_length):
            # Prepare inputs for current step
            if step == input_length:
                # First step - use full input
                model_inputs = {
                    "input_ids": generated_tokens,
                    "attention_mask": attention_mask,
                    "use_cache": generation_config.use_cache,
                    "output_attentions": False,
                    "output_hidden_states": False,
                    "enable_early_exit": generation_config.enable_early_exit,
                }
            else:
                # Subsequent steps - use only last token
                model_inputs = {
                    "input_ids": generated_tokens[:, -1:],
                    "attention_mask": None,
                    "past_key_values": past_key_values,
                    "use_cache": generation_config.use_cache,
                    "output_attentions": False,
                    "output_hidden_states": False,
                    "enable_early_exit": generation_config.enable_early_exit,
                }
            
            # Forward pass
            outputs = self.model(**model_inputs)
            
            # Handle early exit
            if outputs.get("early_exit_outputs") and generation_config.enable_early_exit:
                best_exit = self._select_best_early_exit(outputs["early_exit_outputs"], generation_config.confidence_threshold)
                if best_exit is not None:
                    logits = best_exit["logits"][:, -1, :]
                    early_exit_info["exits_used"].append(best_exit["layer_idx"])
                    early_exit_info["confidence_scores"].append(best_exit["confidence"].item())
                    self.generation_stats["early_exits"] += 1
                else:
                    logits = outputs["logits"][:, -1, :]
            else:
                logits = outputs["logits"][:, -1, :]
            
            # Apply generation parameters
            logits = self._apply_generation_params(logits, generation_config, generated_tokens)
            
            # Sample next token
            if generation_config.do_sample:
                next_token = self._sample_token(logits, generation_config)
            else:
                next_token = torch.argmax(logits, dim=-1, keepdim=True)
            
            # Append to generated sequence
            generated_tokens = torch.cat([generated_tokens, next_token], dim=-1)
            
            # Update attention mask
            attention_mask = torch.cat([
                attention_mask,
                torch.ones((batch_size, 1), device=self.device, dtype=attention_mask.dtype)
            ], dim=-1)
            
            # Update past key values
            if generation_config.use_cache:
                past_key_values = outputs.get("past_key_values")
            
            # Check for EOS token
            if generation_config.eos_token_id is not None:
                if torch.all(next_token == generation_config.eos_token_id):
                    break
        
        # Calculate generation stats
        generation_time = time.time() - start_time
        num_generated = generated_tokens.shape[1] - input_length
        
        generation_stats = {
            "generation_time": generation_time,
            "tokens_per_second": num_generated / generation_time if generation_time > 0 else 0,
            "total_tokens": num_generated,
            "early_exit_usage": len(early_exit_info["exits_used"]) / num_generated if num_generated > 0 else 0,
        }
        
        return GenerationOutput(
            sequences=generated_tokens,
            early_exit_info=early_exit_info,
            generation_stats=generation_stats
        )
    
    def _speculative_generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor,
        generation_config: GenerationConfig,
        max_length: int
    ) -> GenerationOutput:
        """Speculative decoding generation."""
        return self.speculative_decoder.generate(
            input_ids, attention_mask, generation_config, max_length
        )
    
    def _beam_search_generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor,
        generation_config: GenerationConfig,
        max_length: int
    ) -> GenerationOutput:
        """Beam search generation (simplified implementation)."""
        # For now, fall back to greedy generation
        # Full beam search implementation would be more complex
        return self._greedy_generate(input_ids, attention_mask, generation_config, max_length)
    
    def _select_best_early_exit(self, early_exit_outputs: List[Dict], confidence_threshold: float) -> Optional[Dict]:
        """Select the best early exit based on confidence."""
        best_exit = None
        best_confidence = 0.0
        
        for exit_output in early_exit_outputs:
            confidence = exit_output["confidence"].mean().item()
            if confidence > confidence_threshold and confidence > best_confidence:
                best_exit = exit_output
                best_confidence = confidence
        
        return best_exit
    
    def _apply_generation_params(
        self,
        logits: torch.Tensor,
        generation_config: GenerationConfig,
        generated_tokens: torch.Tensor
    ) -> torch.Tensor:
        """Apply temperature, top-k, top-p, and repetition penalty."""
        # Temperature scaling
        if generation_config.temperature != 1.0:
            logits = logits / generation_config.temperature
        
        # Repetition penalty
        if generation_config.repetition_penalty != 1.0:
            for i in range(logits.shape[0]):
                for token in set(generated_tokens[i].tolist()):
                    if logits[i, token] < 0:
                        logits[i, token] *= generation_config.repetition_penalty
                    else:
                        logits[i, token] /= generation_config.repetition_penalty
        
        # Top-k filtering
        if generation_config.top_k > 0:
            top_k = min(generation_config.top_k, logits.size(-1))
            indices_to_remove = logits < torch.topk(logits, top_k)[0][..., -1, None]
            logits[indices_to_remove] = float('-inf')
        
        # Top-p (nucleus) filtering
        if generation_config.top_p < 1.0:
            sorted_logits, sorted_indices = torch.sort(logits, descending=True)
            cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
            
            # Remove tokens with cumulative probability above the threshold
            sorted_indices_to_remove = cumulative_probs > generation_config.top_p
            sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
            sorted_indices_to_remove[..., 0] = 0
            
            indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
            logits[indices_to_remove] = float('-inf')
        
        return logits
    
    def _sample_token(self, logits: torch.Tensor, generation_config: GenerationConfig) -> torch.Tensor:
        """Sample next token from logits."""
        probs = F.softmax(logits, dim=-1)
        next_token = torch.multinomial(probs, num_samples=1)
        return next_token
    
    def clear_cache(self):
        """Clear KV cache to free memory."""
        self.kv_cache.clear()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
        if torch.cuda.is_available():
            return {
                "allocated_gb": torch.cuda.memory_allocated() / 1024**3,
                "reserved_gb": torch.cuda.memory_reserved() / 1024**3,
                "max_allocated_gb": torch.cuda.max_memory_allocated() / 1024**3,
            }
        else:
            return {"allocated_gb": 0, "reserved_gb": 0, "max_allocated_gb": 0}
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """Get generation performance statistics."""
        return self.generation_stats.copy()
