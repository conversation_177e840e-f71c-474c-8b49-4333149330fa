"""
Speculative Decoding for FastLLaMA.

Implements speculative decoding with a draft model for 2-3x speedup in generation.
The draft model shares parameters with the main model for efficiency.
"""

import torch
import torch.nn.functional as F
from typing import Optional, Tuple, List, Dict, Any
import copy

from ..config import FastLLaMAConfig
from ..models import FastLLaMAModel


class DraftModel(torch.nn.Module):
    """
    Lightweight draft model for speculative decoding.
    Shares parameters with the main model but uses fewer layers.
    """
    
    def __init__(self, main_model: FastLLaMAModel, num_draft_layers: int = 8):
        super().__init__()
        self.config = main_model.config
        self.num_draft_layers = num_draft_layers
        
        # Share embeddings with main model
        self.embed_tokens = main_model.embed_tokens
        self.rotary_emb = main_model.rotary_emb
        
        # Use subset of layers from main model
        self.layers = torch.nn.ModuleList(main_model.layers[:num_draft_layers])
        
        # Share final norm and lm_head
        self.norm = main_model.norm
        self.lm_head = main_model.lm_head
        
        # Disable early exit for draft model
        for layer in self.layers:
            if hasattr(layer, 'early_exit_head'):
                layer.early_exit_head = None
            if hasattr(layer, 'confidence_head'):
                layer.confidence_head = None
    
    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_values: Optional[List[torch.FloatTensor]] = None,
        use_cache: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Forward pass through draft model."""
        batch_size, seq_length = input_ids.shape
        
        # Handle past key values
        if past_key_values is None:
            past_length = 0
            past_key_values = tuple([None] * len(self.layers))
        else:
            past_length = past_key_values[0][0].shape[2] if past_key_values[0] is not None else 0
        
        # Position IDs
        if position_ids is None:
            device = input_ids.device
            position_ids = torch.arange(
                past_length, seq_length + past_length, dtype=torch.long, device=device
            )
            position_ids = position_ids.unsqueeze(0).view(-1, seq_length)
        
        # Embeddings
        hidden_states = self.embed_tokens(input_ids)
        
        # Attention mask
        if attention_mask is None:
            attention_mask = torch.ones((batch_size, seq_length), dtype=torch.bool, device=hidden_states.device)
        
        # Create causal mask (simplified)
        causal_mask = torch.triu(torch.ones(seq_length, seq_length), diagonal=1).bool()
        causal_mask = causal_mask.to(hidden_states.device)
        
        # Process through draft layers
        next_decoder_cache = () if use_cache else None
        
        for idx, layer in enumerate(self.layers):
            past_key_value = past_key_values[idx] if past_key_values is not None else None
            
            layer_outputs = layer(
                hidden_states,
                attention_mask=causal_mask,
                position_ids=position_ids,
                past_key_value=past_key_value,
                output_attentions=False,
                use_cache=use_cache,
            )
            
            hidden_states = layer_outputs[0]
            
            if use_cache:
                next_decoder_cache += (layer_outputs[-1],)
        
        # Final norm and projection
        hidden_states = self.norm(hidden_states)
        logits = self.lm_head(hidden_states)
        
        return {
            "logits": logits,
            "past_key_values": next_decoder_cache,
            "hidden_states": hidden_states,
        }


class SpeculativeDecoder:
    """
    Speculative decoding implementation for FastLLaMA.
    
    Uses a lightweight draft model to generate candidate tokens,
    then verifies them with the main model for 2-3x speedup.
    """
    
    def __init__(
        self,
        main_model: FastLLaMAModel,
        config: FastLLaMAConfig,
        num_draft_layers: int = 8,
        lookahead_tokens: int = 4,
    ):
        self.main_model = main_model
        self.config = config
        self.lookahead_tokens = lookahead_tokens
        
        # Create draft model
        self.draft_model = DraftModel(main_model, num_draft_layers)
        self.draft_model.eval()
        
        # Statistics
        self.stats = {
            "total_draft_tokens": 0,
            "accepted_tokens": 0,
            "rejection_count": 0,
            "acceptance_rate": 0.0,
        }
    
    @torch.no_grad()
    def generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor,
        generation_config,
        max_length: int
    ) -> "GenerationOutput":
        """Generate text using speculative decoding."""
        from .engine import GenerationOutput
        
        batch_size, input_length = input_ids.shape
        generated_tokens = input_ids.clone()
        past_key_values_main = None
        past_key_values_draft = None
        
        start_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        end_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        
        if start_time:
            start_time.record()
        
        step = input_length
        while step < max_length:
            # Generate draft tokens
            draft_tokens, draft_probs, past_key_values_draft = self._generate_draft_tokens(
                generated_tokens, past_key_values_draft, generation_config
            )
            
            # Verify draft tokens with main model
            accepted_tokens, past_key_values_main = self._verify_draft_tokens(
                generated_tokens, draft_tokens, draft_probs, past_key_values_main, generation_config
            )
            
            # Update generated sequence
            if len(accepted_tokens) > 0:
                accepted_tensor = torch.tensor(accepted_tokens, device=generated_tokens.device).unsqueeze(0)
                generated_tokens = torch.cat([generated_tokens, accepted_tensor], dim=-1)
                step += len(accepted_tokens)
                
                # Update statistics
                self.stats["accepted_tokens"] += len(accepted_tokens)
            else:
                # If no tokens accepted, generate one token normally
                next_token = self._generate_single_token(generated_tokens, past_key_values_main, generation_config)
                generated_tokens = torch.cat([generated_tokens, next_token], dim=-1)
                step += 1
                self.stats["rejection_count"] += 1
            
            self.stats["total_draft_tokens"] += self.lookahead_tokens
            
            # Check for EOS
            if generation_config.eos_token_id is not None:
                if generated_tokens[0, -1].item() == generation_config.eos_token_id:
                    break
        
        if end_time:
            end_time.record()
            torch.cuda.synchronize()
            generation_time = start_time.elapsed_time(end_time) / 1000.0  # Convert to seconds
        else:
            generation_time = 0.0
        
        # Calculate statistics
        self._update_stats()
        num_generated = generated_tokens.shape[1] - input_length
        
        generation_stats = {
            "generation_time": generation_time,
            "tokens_per_second": num_generated / generation_time if generation_time > 0 else 0,
            "total_tokens": num_generated,
            "acceptance_rate": self.stats["acceptance_rate"],
            "speculative_speedup": self._calculate_speedup(),
        }
        
        return GenerationOutput(
            sequences=generated_tokens,
            generation_stats=generation_stats
        )
    
    def _generate_draft_tokens(
        self,
        input_tokens: torch.Tensor,
        past_key_values: Optional[Tuple],
        generation_config
    ) -> Tuple[List[int], List[torch.Tensor], Tuple]:
        """Generate draft tokens using the lightweight draft model."""
        draft_tokens = []
        draft_probs = []
        current_tokens = input_tokens
        
        for _ in range(self.lookahead_tokens):
            # Forward pass through draft model
            if past_key_values is None:
                # First step - use full sequence
                outputs = self.draft_model(
                    input_ids=current_tokens,
                    use_cache=True
                )
            else:
                # Subsequent steps - use only last token
                outputs = self.draft_model(
                    input_ids=current_tokens[:, -1:],
                    past_key_values=past_key_values,
                    use_cache=True
                )
            
            logits = outputs["logits"][:, -1, :]
            past_key_values = outputs["past_key_values"]
            
            # Apply generation parameters
            logits = self._apply_generation_params(logits, generation_config, current_tokens)
            
            # Sample next token
            probs = F.softmax(logits, dim=-1)
            if generation_config.do_sample:
                next_token = torch.multinomial(probs, num_samples=1)
            else:
                next_token = torch.argmax(logits, dim=-1, keepdim=True)
            
            draft_tokens.append(next_token.item())
            draft_probs.append(probs)
            
            # Update current tokens for next iteration
            current_tokens = torch.cat([current_tokens, next_token], dim=-1)
        
        return draft_tokens, draft_probs, past_key_values
    
    def _verify_draft_tokens(
        self,
        input_tokens: torch.Tensor,
        draft_tokens: List[int],
        draft_probs: List[torch.Tensor],
        past_key_values: Optional[Tuple],
        generation_config
    ) -> Tuple[List[int], Tuple]:
        """Verify draft tokens using the main model."""
        accepted_tokens = []
        
        # Create sequence with all draft tokens
        draft_tensor = torch.tensor(draft_tokens, device=input_tokens.device).unsqueeze(0)
        extended_tokens = torch.cat([input_tokens, draft_tensor], dim=-1)
        
        # Forward pass through main model
        if past_key_values is None:
            # First verification - use full sequence
            outputs = self.main_model(
                input_ids=extended_tokens,
                use_cache=True,
                enable_early_exit=False  # Disable early exit for verification
            )
        else:
            # Subsequent verification - use draft tokens
            outputs = self.main_model(
                input_ids=draft_tensor,
                past_key_values=past_key_values,
                use_cache=True,
                enable_early_exit=False
            )
        
        main_logits = outputs["logits"]
        past_key_values = outputs["past_key_values"]
        
        # Verify each draft token
        for i, (draft_token, draft_prob) in enumerate(zip(draft_tokens, draft_probs)):
            main_logits_step = main_logits[:, input_tokens.shape[1] + i - 1, :]
            main_logits_step = self._apply_generation_params(main_logits_step, generation_config, extended_tokens[:, :input_tokens.shape[1] + i])
            main_prob = F.softmax(main_logits_step, dim=-1)
            
            # Acceptance probability based on the ratio of probabilities
            draft_token_prob = draft_prob[0, draft_token]
            main_token_prob = main_prob[0, draft_token]
            
            acceptance_prob = min(1.0, main_token_prob / (draft_token_prob + 1e-10))
            
            # Accept or reject token
            if torch.rand(1).item() < acceptance_prob:
                accepted_tokens.append(draft_token)
            else:
                # Rejection - sample from adjusted distribution
                adjusted_prob = torch.clamp(main_prob - draft_prob, min=0)
                adjusted_prob = adjusted_prob / adjusted_prob.sum()
                
                if generation_config.do_sample:
                    corrected_token = torch.multinomial(adjusted_prob, num_samples=1)
                    accepted_tokens.append(corrected_token.item())
                else:
                    corrected_token = torch.argmax(adjusted_prob, dim=-1)
                    accepted_tokens.append(corrected_token.item())
                
                break  # Stop after first rejection
        
        return accepted_tokens, past_key_values
    
    def _generate_single_token(
        self,
        input_tokens: torch.Tensor,
        past_key_values: Optional[Tuple],
        generation_config
    ) -> torch.Tensor:
        """Generate a single token using the main model."""
        if past_key_values is None:
            outputs = self.main_model(
                input_ids=input_tokens,
                use_cache=True
            )
        else:
            outputs = self.main_model(
                input_ids=input_tokens[:, -1:],
                past_key_values=past_key_values,
                use_cache=True
            )
        
        logits = outputs["logits"][:, -1, :]
        logits = self._apply_generation_params(logits, generation_config, input_tokens)
        
        if generation_config.do_sample:
            probs = F.softmax(logits, dim=-1)
            next_token = torch.multinomial(probs, num_samples=1)
        else:
            next_token = torch.argmax(logits, dim=-1, keepdim=True)
        
        return next_token
    
    def _apply_generation_params(self, logits: torch.Tensor, generation_config, generated_tokens: torch.Tensor) -> torch.Tensor:
        """Apply generation parameters (simplified version)."""
        # Temperature
        if generation_config.temperature != 1.0:
            logits = logits / generation_config.temperature
        
        # Top-k
        if generation_config.top_k > 0:
            top_k = min(generation_config.top_k, logits.size(-1))
            indices_to_remove = logits < torch.topk(logits, top_k)[0][..., -1, None]
            logits[indices_to_remove] = float('-inf')
        
        return logits
    
    def _update_stats(self):
        """Update acceptance rate statistics."""
        if self.stats["total_draft_tokens"] > 0:
            self.stats["acceptance_rate"] = self.stats["accepted_tokens"] / self.stats["total_draft_tokens"]
    
    def _calculate_speedup(self) -> float:
        """Calculate theoretical speedup from speculative decoding."""
        if self.stats["acceptance_rate"] > 0:
            # Theoretical speedup based on acceptance rate and lookahead
            return 1 + (self.stats["acceptance_rate"] * self.lookahead_tokens)
        return 1.0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get speculative decoding statistics."""
        return self.stats.copy()
    
    def reset_stats(self):
        """Reset statistics."""
        self.stats = {
            "total_draft_tokens": 0,
            "accepted_tokens": 0,
            "rejection_count": 0,
            "acceptance_rate": 0.0,
        }
