"""
Attention mechanisms for FastLLaMA.

Implements hierarchical attention with local, sparse global, and compressed context attention.
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union
from ..config import FastLLaMAConfig


class LocalAttention(nn.Module):
    """Local attention with sliding window."""

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.config = config
        self.window_size = config.local_attention_window
        self.head_dim = config.head_dim
        self.num_heads = config.num_attention_heads

        self.q_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.k_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.v_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.o_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)

        self.scaling = self.head_dim ** -0.5

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Tuple[torch.Tensor]] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:

        batch_size, seq_len, _ = hidden_states.size()

        # Project to Q, K, V
        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)

        # Reshape for multi-head attention
        query_states = query_states.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)

        # Apply local attention mask
        local_mask = self._create_local_mask(seq_len, query_states.device)
        if attention_mask is not None:
            local_mask = local_mask + attention_mask

        # Compute attention scores
        attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) * self.scaling
        attn_weights = attn_weights + local_mask

        # Apply softmax
        attn_weights = F.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query_states.dtype)

        # Apply attention to values
        attn_output = torch.matmul(attn_weights, value_states)

        # Reshape and project output
        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.reshape(batch_size, seq_len, self.config.hidden_size)
        attn_output = self.o_proj(attn_output)

        outputs = (attn_output,)
        if output_attentions:
            outputs += (attn_weights,)
        if use_cache:
            outputs += (past_key_value,)

        return outputs

    def _create_local_mask(self, seq_len: int, device: torch.device) -> torch.Tensor:
        """Create local attention mask with sliding window."""
        mask = torch.full((seq_len, seq_len), float('-inf'), device=device)

        for i in range(seq_len):
            start = max(0, i - self.window_size // 2)
            end = min(seq_len, i + self.window_size // 2 + 1)
            mask[i, start:end] = 0.0

        return mask.unsqueeze(0).unsqueeze(0)


class SparseAttention(nn.Module):
    """Sparse global attention with strided pattern."""

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.config = config
        self.stride = config.sparse_attention_stride
        self.head_dim = config.head_dim
        self.num_heads = config.num_attention_heads

        self.q_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.k_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.v_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.o_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)

        self.scaling = self.head_dim ** -0.5

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Tuple[torch.Tensor]] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:

        batch_size, seq_len, _ = hidden_states.size()

        # Project to Q, K, V
        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)

        # Reshape for multi-head attention
        query_states = query_states.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)

        # Apply sparse attention pattern
        sparse_mask = self._create_sparse_mask(seq_len, query_states.device)
        if attention_mask is not None:
            sparse_mask = sparse_mask + attention_mask

        # Compute attention scores
        attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) * self.scaling
        attn_weights = attn_weights + sparse_mask

        # Apply softmax
        attn_weights = F.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query_states.dtype)

        # Apply attention to values
        attn_output = torch.matmul(attn_weights, value_states)

        # Reshape and project output
        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.reshape(batch_size, seq_len, self.config.hidden_size)
        attn_output = self.o_proj(attn_output)

        outputs = (attn_output,)
        if output_attentions:
            outputs += (attn_weights,)
        if use_cache:
            outputs += (past_key_value,)

        return outputs

    def _create_sparse_mask(self, seq_len: int, device: torch.device) -> torch.Tensor:
        """Create sparse attention mask with strided pattern."""
        mask = torch.full((seq_len, seq_len), float('-inf'), device=device)

        # Allow attention to every stride-th token
        for i in range(seq_len):
            for j in range(0, seq_len, self.stride):
                if j <= i:  # Causal mask
                    mask[i, j] = 0.0

        return mask.unsqueeze(0).unsqueeze(0)


class CompressedAttention(nn.Module):
    """Compressed context attention for long-range dependencies."""

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.config = config
        self.compression_ratio = config.compression_ratio
        self.head_dim = config.head_dim
        self.num_heads = config.num_attention_heads

        self.q_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.k_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.v_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)
        self.o_proj = nn.Linear(config.hidden_size, config.hidden_size, bias=False)

        # Compression layers
        self.compressor = nn.Sequential(
            nn.Linear(config.hidden_size, config.hidden_size // 2),
            nn.ReLU(),
            nn.Linear(config.hidden_size // 2, config.hidden_size)
        )

        self.scaling = self.head_dim ** -0.5

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Tuple[torch.Tensor]] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:

        batch_size, seq_len, _ = hidden_states.size()

        # Compress the sequence for long-range attention
        compressed_states = self._compress_sequence(hidden_states)
        compressed_len = compressed_states.size(1)

        # Project to Q, K, V
        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(compressed_states)
        value_states = self.v_proj(compressed_states)

        # Reshape for multi-head attention
        query_states = query_states.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(batch_size, compressed_len, self.num_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(batch_size, compressed_len, self.num_heads, self.head_dim).transpose(1, 2)

        # Compute attention scores
        attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) * self.scaling

        # Apply softmax
        attn_weights = F.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query_states.dtype)

        # Apply attention to values
        attn_output = torch.matmul(attn_weights, value_states)

        # Reshape and project output
        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.reshape(batch_size, seq_len, self.config.hidden_size)
        attn_output = self.o_proj(attn_output)

        outputs = (attn_output,)
        if output_attentions:
            outputs += (attn_weights,)
        if use_cache:
            outputs += (past_key_value,)

        return outputs

    def _compress_sequence(self, hidden_states: torch.Tensor) -> torch.Tensor:
        """Compress sequence using learned compression."""
        batch_size, seq_len, hidden_size = hidden_states.size()

        if seq_len <= self.compression_ratio:
            return hidden_states

        # Reshape for compression
        compressed_len = seq_len // self.compression_ratio
        remainder = seq_len % self.compression_ratio

        if remainder > 0:
            # Pad to make divisible
            padding = self.compression_ratio - remainder
            hidden_states = F.pad(hidden_states, (0, 0, 0, padding))
            seq_len += padding
            compressed_len = seq_len // self.compression_ratio

        # Reshape and compress
        reshaped = hidden_states.view(batch_size, compressed_len, self.compression_ratio, hidden_size)
        compressed = reshaped.mean(dim=2)  # Average pooling
        compressed = self.compressor(compressed)

        return compressed


class HierarchicalAttention(nn.Module):
    """Hierarchical attention combining local, sparse, and compressed attention."""

    def __init__(self, config: FastLLaMAConfig, layer_idx: int):
        super().__init__()
        self.config = config
        self.layer_idx = layer_idx

        # Initialize attention mechanisms based on layer
        self.local_attention = LocalAttention(config)

        if layer_idx in config.sparse_layers or layer_idx in config.hierarchical_layers or layer_idx in config.full_attention_layers:
            self.sparse_attention = SparseAttention(config)
        else:
            self.sparse_attention = None

        if layer_idx in config.hierarchical_layers or layer_idx in config.full_attention_layers:
            self.compressed_attention = CompressedAttention(config)
        else:
            self.compressed_attention = None

        # Combination weights
        self.attention_weights = nn.Parameter(torch.ones(self._get_num_attentions()))

    def _get_num_attentions(self) -> int:
        """Get number of attention mechanisms for this layer."""
        count = 1  # Always have local attention
        if self.sparse_attention is not None:
            count += 1
        if self.compressed_attention is not None:
            count += 1
        return count

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Tuple[torch.Tensor]] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:

        seq_len = hidden_states.size(1)

        # For short sequences, use only local attention for efficiency
        if seq_len <= 512:
            return self.local_attention(
                hidden_states, attention_mask, position_ids, past_key_value, output_attentions, use_cache
            )

        # Local attention (always present)
        local_output = self.local_attention(
            hidden_states, attention_mask, position_ids, past_key_value, output_attentions, use_cache
        )[0]

        outputs = [local_output]
        weights = [self.attention_weights[0]]

        # Sparse attention (if enabled for this layer and beneficial)
        if self.sparse_attention is not None and seq_len > 1024:
            sparse_output = self.sparse_attention(
                hidden_states, attention_mask, position_ids, past_key_value, output_attentions, use_cache
            )[0]
            outputs.append(sparse_output)
            weights.append(self.attention_weights[1])

        # Compressed attention (if enabled for this layer and beneficial)
        if self.compressed_attention is not None and seq_len > 2048:
            compressed_output = self.compressed_attention(
                hidden_states, attention_mask, position_ids, past_key_value, output_attentions, use_cache
            )[0]
            outputs.append(compressed_output)
            weights.append(self.attention_weights[-1])

        # If only one attention mechanism, return it directly
        if len(outputs) == 1:
            return (outputs[0],)

        # Combine outputs with learned weights
        weights = F.softmax(torch.stack(weights), dim=0)
        combined_output = sum(w * out for w, out in zip(weights, outputs))

        return (combined_output,)


class EnhancedGroupedQueryAttention(nn.Module):
    """Enhanced Grouped Query Attention with adaptive grouping and KV cache optimization."""

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.config = config
        self.hidden_size = config.hidden_size
        self.num_heads = config.num_attention_heads
        self.num_key_value_heads = config.num_key_value_heads
        self.num_key_value_groups = self.num_heads // self.num_key_value_heads
        self.head_dim = self.hidden_size // self.num_heads

        # Projections
        self.q_proj = nn.Linear(self.hidden_size, self.num_heads * self.head_dim, bias=False)
        self.k_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=False)
        self.v_proj = nn.Linear(self.hidden_size, self.num_key_value_heads * self.head_dim, bias=False)
        self.o_proj = nn.Linear(self.num_heads * self.head_dim, self.hidden_size, bias=False)

        # Adaptive grouping
        self.group_adapter = nn.Linear(self.hidden_size, self.num_key_value_heads)

        # KV cache compression
        if config.kv_cache_quantization:
            self.k_quantizer = nn.Parameter(torch.ones(1))
            self.v_quantizer = nn.Parameter(torch.ones(1))

        self.scaling = self.head_dim ** -0.5

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Tuple[torch.Tensor]] = None,
        output_attentions: bool = False,
        use_cache: bool = False,
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]]]:

        batch_size, seq_len, _ = hidden_states.size()

        # Project to Q, K, V
        query_states = self.q_proj(hidden_states)
        key_states = self.k_proj(hidden_states)
        value_states = self.v_proj(hidden_states)

        # Reshape
        query_states = query_states.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        key_states = key_states.view(batch_size, seq_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)
        value_states = value_states.view(batch_size, seq_len, self.num_key_value_heads, self.head_dim).transpose(1, 2)

        # KV cache quantization (before concatenation)
        if self.config.kv_cache_quantization and use_cache:
            key_states = self._quantize_kv(key_states, self.k_quantizer)
            value_states = self._quantize_kv(value_states, self.v_quantizer)

        # Handle past key values (before repetition)
        if past_key_value is not None:
            # Past key values should have the same shape as current key/value states
            if isinstance(past_key_value, (tuple, list)) and len(past_key_value) >= 2:
                past_key, past_value = past_key_value[0], past_key_value[1]
                # Ensure past key/value have correct dimensions
                if past_key.shape[1] == self.num_key_value_heads and past_key.shape[3] == self.head_dim:
                    key_states = torch.cat([past_key, key_states], dim=2)
                    value_states = torch.cat([past_value, value_states], dim=2)

        # Store original key/value states for cache (before repetition)
        if use_cache:
            cache_key_states = key_states
            cache_value_states = value_states

        # Repeat KV heads for GQA
        key_states = self._repeat_kv(key_states, self.num_key_value_groups)
        value_states = self._repeat_kv(value_states, self.num_key_value_groups)

        # Compute attention
        attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) * self.scaling

        if attention_mask is not None:
            attn_weights = attn_weights + attention_mask

        # Apply softmax
        attn_weights = F.softmax(attn_weights, dim=-1, dtype=torch.float32).to(query_states.dtype)

        # Apply attention to values
        attn_output = torch.matmul(attn_weights, value_states)

        # Reshape and project output
        attn_output = attn_output.transpose(1, 2).contiguous()
        attn_output = attn_output.reshape(batch_size, seq_len, self.hidden_size)
        attn_output = self.o_proj(attn_output)

        outputs = (attn_output,)
        if output_attentions:
            outputs += (attn_weights,)
        if use_cache:
            # Store original (non-repeated) key/value states for next iteration
            outputs += ((cache_key_states, cache_value_states),)

        return outputs

    def _repeat_kv(self, hidden_states: torch.Tensor, n_rep: int) -> torch.Tensor:
        """Repeat key/value heads for grouped query attention."""
        batch, num_key_value_heads, slen, head_dim = hidden_states.shape
        if n_rep == 1:
            return hidden_states
        hidden_states = hidden_states[:, :, None, :, :].expand(batch, num_key_value_heads, n_rep, slen, head_dim)
        return hidden_states.reshape(batch, num_key_value_heads * n_rep, slen, head_dim)

    def _quantize_kv(self, states: torch.Tensor, quantizer: torch.Tensor) -> torch.Tensor:
        """Quantize key/value states for memory efficiency."""
        if self.training:
            return states

        # Simple quantization scheme
        scale = quantizer.abs()
        quantized = torch.round(states / scale) * scale
        return quantized
