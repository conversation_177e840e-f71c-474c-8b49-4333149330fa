"""
Core layers for FastLLaMA model.

Implements the fundamental building blocks including decoder layers, MLP, embeddings,
and rotary position embeddings with optimizations.
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
from ..config import FastLLaMAConfig
from .attention import HierarchicalAttention, EnhancedGroupedQueryAttention


class FastLLaMARotaryEmbedding(nn.Module):
    """Rotary Position Embedding (RoPE) with optimizations."""

    def __init__(self, dim: int, max_position_embeddings: int = 2048, base: float = 10000.0):
        super().__init__()
        self.dim = dim
        self.max_position_embeddings = max_position_embeddings
        self.base = base

        # Precompute frequency tensor
        inv_freq = 1.0 / (base ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq, persistent=False)

        # Cache for cos and sin values
        self._cos_cached = None
        self._sin_cached = None
        self._seq_len_cached = 0

    def forward(self, x: torch.Tensor, seq_len: int) -> Tuple[torch.Tensor, torch.Tensor]:
        # Use cached values if available
        if seq_len <= self._seq_len_cached and self._cos_cached is not None:
            return (
                self._cos_cached[:seq_len].to(x.device),
                self._sin_cached[:seq_len].to(x.device)
            )

        # Compute new values
        t = torch.arange(seq_len, device=x.device, dtype=self.inv_freq.dtype)
        freqs = torch.outer(t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)

        cos = emb.cos()
        sin = emb.sin()

        # Cache the values
        self._cos_cached = cos
        self._sin_cached = sin
        self._seq_len_cached = seq_len

        return cos.to(x.dtype), sin.to(x.dtype)


def rotate_half(x: torch.Tensor) -> torch.Tensor:
    """Rotates half the hidden dims of the input."""
    x1 = x[..., : x.shape[-1] // 2]
    x2 = x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)


def apply_rotary_pos_emb(q: torch.Tensor, k: torch.Tensor, cos: torch.Tensor, sin: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
    """Apply rotary position embedding to query and key tensors."""
    q_embed = (q * cos) + (rotate_half(q) * sin)
    k_embed = (k * cos) + (rotate_half(k) * sin)
    return q_embed, k_embed


class FastLLaMAMLP(nn.Module):
    """
    Multi-Layer Perceptron with SwiGLU activation.
    Optimized version of the LLaMA MLP with memory efficiency improvements.
    """

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.config = config
        self.hidden_size = config.hidden_size
        self.intermediate_size = config.intermediate_size

        # SwiGLU components
        self.gate_proj = nn.Linear(self.hidden_size, self.intermediate_size, bias=False)
        self.up_proj = nn.Linear(self.hidden_size, self.intermediate_size, bias=False)
        self.down_proj = nn.Linear(self.intermediate_size, self.hidden_size, bias=False)

        # Activation function
        self.act_fn = F.silu

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # SwiGLU: gate_proj(x) * silu(up_proj(x))
        gate = self.act_fn(self.gate_proj(x))
        up = self.up_proj(x)
        intermediate = gate * up
        return self.down_proj(intermediate)


class FastLLaMAEmbedding(nn.Module):
    """Enhanced embedding layer with parameter sharing capabilities."""

    def __init__(self, config: FastLLaMAConfig):
        super().__init__()
        self.config = config
        self.vocab_size = config.vocab_size
        self.hidden_size = config.hidden_size

        # Main embedding
        self.embed_tokens = nn.Embedding(config.vocab_size, config.hidden_size)

        # Parameter sharing for similar token types (if enabled)
        if config.parameter_sharing:
            self.token_type_embeddings = nn.Embedding(8, config.hidden_size)  # 8 token types
            self.token_type_map = self._create_token_type_map()

        # Embedding scaling
        self.embed_scale = math.sqrt(config.hidden_size)

    def forward(self, input_ids: torch.Tensor) -> torch.Tensor:
        embeddings = self.embed_tokens(input_ids) * self.embed_scale

        # Add token type embeddings if parameter sharing is enabled
        if self.config.parameter_sharing:
            token_types = self._get_token_types(input_ids)
            type_embeddings = self.token_type_embeddings(token_types)
            embeddings = embeddings + type_embeddings

        return embeddings

    def _create_token_type_map(self) -> torch.Tensor:
        """Create mapping from token IDs to token types for parameter sharing."""
        # Simple heuristic mapping - can be improved with actual token analysis
        token_type_map = torch.zeros(self.vocab_size, device=self.config.device, dtype=torch.long)

        # Map tokens to types based on ID ranges (simplified)
        for i in range(self.vocab_size):
            if i < 100:  # Special tokens
                token_type_map[i] = 0
            elif i < 1000:  # Common words
                token_type_map[i] = 1
            elif i < 5000:  # Medium frequency
                token_type_map[i] = 2
            elif i < 10000:  # Lower frequency
                token_type_map[i] = 3
            elif i < 20000:  # Rare words
                token_type_map[i] = 4
            elif i < 25000:  # Numbers/punctuation
                token_type_map[i] = 5
            elif i < 30000:  # Subwords
                token_type_map[i] = 6
            else:  # Very rare
                token_type_map[i] = 7

        return token_type_map

    def _get_token_types(self, input_ids: torch.Tensor) -> torch.Tensor:
        """Get token types for input IDs."""
        return self.token_type_map[input_ids]


class FastLLaMADecoderLayer(nn.Module):
    """
    FastLLaMA decoder layer with hierarchical attention and optimizations.
    """

    def __init__(self, config: FastLLaMAConfig, layer_idx: int):
        super().__init__()
        self.config = config
        self.layer_idx = layer_idx
        self.hidden_size = config.hidden_size

        # Choose attention mechanism based on layer index and configuration
        if config.num_key_value_heads < config.num_attention_heads:
            # Use Enhanced GQA for efficiency
            self.self_attn = EnhancedGroupedQueryAttention(config)
        else:
            # Use hierarchical attention for long context
            self.self_attn = HierarchicalAttention(config, layer_idx)

        # MLP
        self.mlp = FastLLaMAMLP(config)

        # Layer normalization (RMSNorm)
        self.input_layernorm = FastLLaMARMSNorm(config.hidden_size, eps=config.rms_norm_eps)
        self.post_attention_layernorm = FastLLaMARMSNorm(config.hidden_size, eps=config.rms_norm_eps)

        # Early exit components (if this is an early exit layer)
        if layer_idx in config.early_exit_layers and config.enable_early_exit:
            self.early_exit_head = nn.Linear(config.hidden_size, config.vocab_size, bias=False)
            self.confidence_head = nn.Linear(config.hidden_size, 1)
        else:
            self.early_exit_head = None
            self.confidence_head = None

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.LongTensor] = None,
        past_key_value: Optional[Tuple[torch.Tensor]] = None,
        output_attentions: Optional[bool] = False,
        use_cache: Optional[bool] = False,
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[Tuple[torch.Tensor]], Optional[dict]]:

        residual = hidden_states

        # Pre-attention layer norm
        hidden_states = self.input_layernorm(hidden_states)

        # Self-attention
        attn_outputs = self.self_attn(
            hidden_states=hidden_states,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_value=past_key_value,
            output_attentions=output_attentions,
            use_cache=use_cache,
        )

        hidden_states = attn_outputs[0]

        # Residual connection
        hidden_states = residual + hidden_states

        # Pre-MLP layer norm
        residual = hidden_states
        hidden_states = self.post_attention_layernorm(hidden_states)

        # MLP
        hidden_states = self.mlp(hidden_states)

        # Residual connection
        hidden_states = residual + hidden_states

        # Early exit logic
        early_exit_output = None
        if self.early_exit_head is not None and not self.training:
            # Use max pooling for more confident predictions
            pooled_hidden = torch.max(hidden_states, dim=1)[0]
            confidence = torch.sigmoid(self.confidence_head(pooled_hidden))

            # Add some randomness to encourage early exits during testing
            if self.training:
                confidence = confidence + 0.2  # Boost confidence during training

            if confidence.mean() > self.config.confidence_threshold:
                early_exit_logits = self.early_exit_head(hidden_states)
                early_exit_output = {
                    "logits": early_exit_logits,
                    "confidence": confidence,
                    "layer_idx": self.layer_idx
                }

        outputs = (hidden_states,)
        if output_attentions:
            outputs += (attn_outputs[1],)
        if use_cache:
            outputs += (attn_outputs[-1],)
        if early_exit_output is not None:
            outputs += (early_exit_output,)

        return outputs


class FastLLaMARMSNorm(nn.Module):
    """Root Mean Square Layer Normalization."""

    def __init__(self, hidden_size: int, eps: float = 1e-6):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(hidden_size))
        self.variance_epsilon = eps

    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        input_dtype = hidden_states.dtype
        hidden_states = hidden_states.to(torch.float32)
        variance = hidden_states.pow(2).mean(-1, keepdim=True)
        hidden_states = hidden_states * torch.rsqrt(variance + self.variance_epsilon)
        return self.weight * hidden_states.to(input_dtype)
