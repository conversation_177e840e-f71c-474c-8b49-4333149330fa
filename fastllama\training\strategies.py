"""
Training strategies for FastLLaMA.

Implements the three-phase training strategy from the system design:
1. Foundation Training (70% of compute)
2. Long Context Training (20% of compute)  
3. Efficiency Fine-tuning (10% of compute)
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import math

from ..config import FastLLa<PERSON>Config


@dataclass
class PhaseConfig:
    """Configuration for a training phase."""
    name: str
    steps: int
    seq_length: int
    batch_size: int
    learning_rate_multiplier: float = 1.0
    enable_compression: bool = True
    train_early_exit: bool = False
    gradient_accumulation_steps: int = 1
    special_settings: Optional[Dict[str, Any]] = None


class TrainingStrategy(ABC):
    """Abstract base class for training strategies."""
    
    def __init__(self, config: FastLLaMAConfig, training_args):
        self.config = config
        self.training_args = training_args
    
    @abstractmethod
    def get_training_phases(self, total_steps: int) -> Dict[str, PhaseConfig]:
        """Get training phases configuration."""
        pass


class PhaseBasedStrategy(TrainingStrategy):
    """
    Three-phase training strategy for FastLLaMA:
    
    Phase 1: Foundation Training (70% of compute)
    - Standard next-token prediction
    - Sequence lengths: 2K → 8K → 32K
    - Focus on compression module training
    
    Phase 2: Long Context Training (20% of compute)  
    - Extended sequences up to 1M tokens
    - Hierarchical attention pattern learning
    - Context compression optimization
    
    Phase 3: Efficiency Fine-tuning (10% of compute)
    - Early exit calibration
    - Dynamic layer scaling optimization
    - Performance profiling and adjustment
    """
    
    def get_training_phases(self, total_steps: int) -> Dict[str, PhaseConfig]:
        """Get the three training phases."""
        # Calculate steps for each phase
        foundation_steps = int(total_steps * self.training_args.foundation_phase_ratio)
        long_context_steps = int(total_steps * self.training_args.long_context_phase_ratio)
        efficiency_steps = total_steps - foundation_steps - long_context_steps
        
        phases = {}
        
        # Phase 1: Foundation Training
        phases["foundation"] = self._get_foundation_phase(foundation_steps)
        
        # Phase 2: Long Context Training
        phases["long_context"] = self._get_long_context_phase(long_context_steps)
        
        # Phase 3: Efficiency Fine-tuning
        phases["efficiency"] = self._get_efficiency_phase(efficiency_steps)
        
        return phases
    
    def _get_foundation_phase(self, steps: int) -> PhaseConfig:
        """Foundation training phase configuration."""
        # Progressive sequence length increase
        # Start with 2K, gradually increase to 32K
        base_seq_length = self.training_args.initial_seq_length
        max_seq_length = min(32768, self.training_args.max_seq_length)
        
        return PhaseConfig(
            name="Foundation Training",
            steps=steps,
            seq_length=base_seq_length,  # Will be progressively increased
            batch_size=self.training_args.per_device_train_batch_size,
            learning_rate_multiplier=1.0,
            enable_compression=True,
            train_early_exit=False,
            gradient_accumulation_steps=self.training_args.gradient_accumulation_steps,
            special_settings={
                "progressive_seq_length": True,
                "seq_length_schedule": self._create_seq_length_schedule(
                    steps, base_seq_length, max_seq_length
                ),
                "focus_compression": True,
                "compression_loss_weight": 0.1,
            }
        )
    
    def _get_long_context_phase(self, steps: int) -> PhaseConfig:
        """Long context training phase configuration."""
        # Use maximum sequence length for long context training
        max_seq_length = self.training_args.max_seq_length
        
        # Reduce batch size for longer sequences
        batch_size = max(1, self.training_args.per_device_train_batch_size // 2)
        
        return PhaseConfig(
            name="Long Context Training",
            steps=steps,
            seq_length=max_seq_length,
            batch_size=batch_size,
            learning_rate_multiplier=0.5,  # Lower LR for stability
            enable_compression=True,
            train_early_exit=False,
            gradient_accumulation_steps=self.training_args.gradient_accumulation_steps * 2,
            special_settings={
                "hierarchical_attention_focus": True,
                "compression_optimization": True,
                "long_range_dependencies": True,
                "memory_efficient_attention": True,
            }
        )
    
    def _get_efficiency_phase(self, steps: int) -> PhaseConfig:
        """Efficiency fine-tuning phase configuration."""
        # Use medium sequence length for efficiency training
        seq_length = min(8192, self.training_args.max_seq_length)
        
        return PhaseConfig(
            name="Efficiency Fine-tuning",
            steps=steps,
            seq_length=seq_length,
            batch_size=self.training_args.per_device_train_batch_size,
            learning_rate_multiplier=0.1,  # Very low LR for fine-tuning
            enable_compression=True,
            train_early_exit=True,  # Enable early exit training
            gradient_accumulation_steps=self.training_args.gradient_accumulation_steps,
            special_settings={
                "early_exit_calibration": True,
                "confidence_threshold_tuning": True,
                "dynamic_layer_scaling": True,
                "performance_optimization": True,
                "early_exit_loss_weight": 0.2,
                "confidence_loss_weight": 0.1,
            }
        )
    
    def _create_seq_length_schedule(self, total_steps: int, min_length: int, max_length: int) -> List[int]:
        """Create progressive sequence length schedule."""
        schedule = []
        
        # Define sequence length milestones
        milestones = [
            (0.0, min_length),      # Start: 2K
            (0.3, min_length * 2),  # 30%: 4K
            (0.6, min_length * 4),  # 60%: 8K
            (0.8, min_length * 8),  # 80%: 16K
            (1.0, max_length),      # 100%: 32K
        ]
        
        for step in range(total_steps):
            progress = step / total_steps
            
            # Find current milestone
            for i, (milestone_progress, seq_length) in enumerate(milestones):
                if progress <= milestone_progress:
                    schedule.append(seq_length)
                    break
            else:
                schedule.append(max_length)
        
        return schedule


class CurriculumStrategy(TrainingStrategy):
    """Curriculum learning strategy with gradual complexity increase."""
    
    def get_training_phases(self, total_steps: int) -> Dict[str, PhaseConfig]:
        """Get curriculum-based training phases."""
        # Divide into multiple phases with increasing complexity
        num_phases = 5
        steps_per_phase = total_steps // num_phases
        
        phases = {}
        
        for i in range(num_phases):
            phase_name = f"curriculum_phase_{i+1}"
            complexity_level = (i + 1) / num_phases
            
            phases[phase_name] = PhaseConfig(
                name=f"Curriculum Phase {i+1}",
                steps=steps_per_phase,
                seq_length=int(self.training_args.initial_seq_length * (1 + complexity_level)),
                batch_size=max(1, int(self.training_args.per_device_train_batch_size * (1.5 - complexity_level))),
                learning_rate_multiplier=1.0 - 0.1 * i,
                enable_compression=i >= 2,  # Enable compression from phase 3
                train_early_exit=i >= 3,    # Enable early exit from phase 4
                special_settings={
                    "complexity_level": complexity_level,
                    "curriculum_weight": 1.0 - complexity_level,
                }
            )
        
        return phases


class AdaptiveStrategy(TrainingStrategy):
    """Adaptive training strategy that adjusts based on training metrics."""
    
    def __init__(self, config: FastLLaMAConfig, training_args):
        super().__init__(config, training_args)
        self.performance_history = []
        self.adaptation_threshold = 0.05
    
    def get_training_phases(self, total_steps: int) -> Dict[str, PhaseConfig]:
        """Get adaptive training phases."""
        # Start with a base phase
        base_phase = PhaseConfig(
            name="Adaptive Training",
            steps=total_steps,
            seq_length=self.training_args.initial_seq_length,
            batch_size=self.training_args.per_device_train_batch_size,
            learning_rate_multiplier=1.0,
            enable_compression=True,
            train_early_exit=False,
            special_settings={
                "adaptive_mode": True,
                "adaptation_interval": 1000,
            }
        )
        
        return {"adaptive": base_phase}
    
    def adapt_phase(self, current_phase: PhaseConfig, metrics: Dict[str, float]) -> PhaseConfig:
        """Adapt training phase based on current metrics."""
        self.performance_history.append(metrics)
        
        # Only adapt if we have enough history
        if len(self.performance_history) < 5:
            return current_phase
        
        # Calculate performance trend
        recent_losses = [m.get("loss", float('inf')) for m in self.performance_history[-5:]]
        loss_trend = (recent_losses[-1] - recent_losses[0]) / recent_losses[0]
        
        # Adapt based on performance
        new_phase = PhaseConfig(**current_phase.__dict__)
        
        if loss_trend > self.adaptation_threshold:
            # Performance degrading - reduce complexity
            new_phase.seq_length = max(512, int(current_phase.seq_length * 0.8))
            new_phase.learning_rate_multiplier *= 0.8
            new_phase.batch_size = min(32, current_phase.batch_size + 1)
        elif loss_trend < -self.adaptation_threshold:
            # Performance improving - increase complexity
            new_phase.seq_length = min(self.training_args.max_seq_length, int(current_phase.seq_length * 1.2))
            new_phase.learning_rate_multiplier *= 1.1
            new_phase.batch_size = max(1, current_phase.batch_size - 1)
        
        return new_phase


class MultiTaskStrategy(TrainingStrategy):
    """Multi-task training strategy for different capabilities."""
    
    def get_training_phases(self, total_steps: int) -> Dict[str, PhaseConfig]:
        """Get multi-task training phases."""
        # Divide steps among different tasks
        task_steps = total_steps // 4
        
        phases = {
            "language_modeling": PhaseConfig(
                name="Language Modeling",
                steps=task_steps,
                seq_length=self.training_args.initial_seq_length,
                batch_size=self.training_args.per_device_train_batch_size,
                learning_rate_multiplier=1.0,
                enable_compression=False,
                train_early_exit=False,
                special_settings={"task_type": "language_modeling"}
            ),
            
            "long_context": PhaseConfig(
                name="Long Context Understanding",
                steps=task_steps,
                seq_length=self.training_args.max_seq_length,
                batch_size=max(1, self.training_args.per_device_train_batch_size // 2),
                learning_rate_multiplier=0.8,
                enable_compression=True,
                train_early_exit=False,
                special_settings={"task_type": "long_context"}
            ),
            
            "reasoning": PhaseConfig(
                name="Reasoning Tasks",
                steps=task_steps,
                seq_length=4096,
                batch_size=self.training_args.per_device_train_batch_size,
                learning_rate_multiplier=0.6,
                enable_compression=True,
                train_early_exit=True,
                special_settings={"task_type": "reasoning"}
            ),
            
            "efficiency": PhaseConfig(
                name="Efficiency Optimization",
                steps=task_steps,
                seq_length=2048,
                batch_size=self.training_args.per_device_train_batch_size * 2,
                learning_rate_multiplier=0.2,
                enable_compression=True,
                train_early_exit=True,
                special_settings={"task_type": "efficiency"}
            ),
        }
        
        return phases
