"""
Memory optimization utilities for FastLLaMA.

Implements memory-efficient training and inference optimizations including:
- Gradient checkpointing strategies
- KV cache management
- Memory monitoring and optimization
"""

import torch
import torch.nn as nn
from typing import Dict, List, Optional, Tuple, Any, Callable
import gc
import psutil
import threading
import time
from contextlib import contextmanager


class MemoryOptimizer:
    """
    Memory optimization manager for FastLLaMA training and inference.
    """
    
    def __init__(self, model: nn.Module, config):
        self.model = model
        self.config = config
        self.memory_stats = {}
        self.optimization_enabled = True
        
    def optimize_for_training(self):
        """Apply memory optimizations for training."""
        if not self.optimization_enabled:
            return
        
        # Enable gradient checkpointing
        if self.config.use_gradient_checkpointing:
            self._enable_gradient_checkpointing()
        
        # Optimize parameter storage
        self._optimize_parameter_storage()
        
        # Setup memory monitoring
        self._setup_memory_monitoring()
    
    def optimize_for_inference(self):
        """Apply memory optimizations for inference."""
        if not self.optimization_enabled:
            return
        
        # Disable gradient computation
        for param in self.model.parameters():
            param.requires_grad = False
        
        # Optimize model for inference
        self.model.eval()
        
        # Enable KV cache optimization
        self._optimize_kv_cache()
    
    def _enable_gradient_checkpointing(self):
        """Enable selective gradient checkpointing."""
        checkpointer = GradientCheckpointer(self.config)
        
        for i, layer in enumerate(self.model.layers):
            if checkpointer.should_checkpoint_layer(i):
                layer.gradient_checkpointing = True
    
    def _optimize_parameter_storage(self):
        """Optimize parameter storage and sharing."""
        if not self.config.parameter_sharing:
            return
        
        # Group similar parameters for sharing
        parameter_groups = self._group_similar_parameters()
        
        for group in parameter_groups:
            if len(group) > 1:
                # Share parameters within group
                base_param = group[0]
                for param in group[1:]:
                    param.data = base_param.data
    
    def _group_similar_parameters(self) -> List[List[nn.Parameter]]:
        """Group parameters that can share storage."""
        groups = []
        
        # Group by shape and type
        shape_groups = {}
        for name, param in self.model.named_parameters():
            if "embed" in name or "norm" in name:
                continue  # Skip embeddings and norms
            
            key = (param.shape, param.dtype)
            if key not in shape_groups:
                shape_groups[key] = []
            shape_groups[key].append(param)
        
        # Only group if parameters are similar enough
        for params in shape_groups.values():
            if len(params) > 1:
                groups.append(params)
        
        return groups
    
    def _optimize_kv_cache(self):
        """Optimize KV cache for inference."""
        for layer in self.model.layers:
            if hasattr(layer.self_attn, 'k_quantizer'):
                # Enable KV cache quantization
                layer.self_attn.use_quantized_cache = True
    
    def _setup_memory_monitoring(self):
        """Setup continuous memory monitoring."""
        self.memory_monitor = MemoryMonitor()
        self.memory_monitor.start()
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics."""
        return get_memory_stats()
    
    def clear_cache(self):
        """Clear all caches to free memory."""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
    
    def enable_optimization(self):
        """Enable memory optimizations."""
        self.optimization_enabled = True
    
    def disable_optimization(self):
        """Disable memory optimizations."""
        self.optimization_enabled = False


class GradientCheckpointer:
    """
    Intelligent gradient checkpointing for FastLLaMA.
    
    Implements selective checkpointing based on layer importance and memory constraints.
    """
    
    def __init__(self, config):
        self.config = config
        self.checkpointing_ratio = getattr(config, 'gradient_checkpointing_ratio', 0.5)
        self.layer_importance = self._calculate_layer_importance()
    
    def should_checkpoint_layer(self, layer_idx: int) -> bool:
        """Determine if a layer should use gradient checkpointing."""
        # Checkpoint based on importance and ratio
        total_layers = self.config.num_hidden_layers
        
        # Always checkpoint middle layers (they have less impact on final output)
        if 0.3 * total_layers <= layer_idx <= 0.7 * total_layers:
            return True
        
        # Checkpoint based on importance score
        importance = self.layer_importance.get(layer_idx, 0.5)
        return importance < self.checkpointing_ratio
    
    def _calculate_layer_importance(self) -> Dict[int, float]:
        """Calculate importance scores for each layer."""
        total_layers = self.config.num_hidden_layers
        importance = {}
        
        for i in range(total_layers):
            # Early layers are more important for feature extraction
            # Later layers are more important for final predictions
            # Middle layers are less important
            
            if i < total_layers * 0.2:  # First 20%
                importance[i] = 0.8
            elif i > total_layers * 0.8:  # Last 20%
                importance[i] = 0.9
            else:  # Middle 60%
                importance[i] = 0.3
        
        return importance


class KVCacheManager:
    """
    KV cache management for efficient inference.
    
    Handles cache quantization, compression, and memory management.
    """
    
    def __init__(self, config):
        self.config = config
        self.cache_storage = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "memory_usage": 0
        }
        self.max_cache_size = getattr(config, 'max_cache_size', 1024 * 1024 * 1024)  # 1GB
    
    def get_cache(self, layer_idx: int, batch_idx: int) -> Optional[Tuple[torch.Tensor, torch.Tensor]]:
        """Get cached key-value tensors."""
        cache_key = (layer_idx, batch_idx)
        
        if cache_key in self.cache_storage:
            self.cache_stats["hits"] += 1
            return self.cache_storage[cache_key]
        else:
            self.cache_stats["misses"] += 1
            return None
    
    def set_cache(self, layer_idx: int, batch_idx: int, key: torch.Tensor, value: torch.Tensor):
        """Store key-value tensors in cache."""
        cache_key = (layer_idx, batch_idx)
        
        # Quantize if enabled
        if self.config.kv_cache_quantization:
            key = self._quantize_tensor(key)
            value = self._quantize_tensor(value)
        
        # Check memory limit
        cache_size = self._estimate_tensor_size(key) + self._estimate_tensor_size(value)
        
        if self.cache_stats["memory_usage"] + cache_size > self.max_cache_size:
            self._evict_cache()
        
        self.cache_storage[cache_key] = (key, value)
        self.cache_stats["memory_usage"] += cache_size
    
    def _quantize_tensor(self, tensor: torch.Tensor) -> torch.Tensor:
        """Quantize tensor for memory efficiency."""
        if tensor.dtype == torch.float32:
            # Quantize to int8
            scale = tensor.abs().max() / 127
            quantized = torch.round(tensor / scale).clamp(-128, 127).to(torch.int8)
            # Store scale for dequantization
            quantized._scale = scale
            return quantized
        return tensor
    
    def _dequantize_tensor(self, tensor: torch.Tensor) -> torch.Tensor:
        """Dequantize tensor back to original precision."""
        if hasattr(tensor, '_scale'):
            return tensor.float() * tensor._scale
        return tensor
    
    def _estimate_tensor_size(self, tensor: torch.Tensor) -> int:
        """Estimate memory size of tensor in bytes."""
        return tensor.numel() * tensor.element_size()
    
    def _evict_cache(self):
        """Evict least recently used cache entries."""
        # Simple LRU eviction - remove oldest entries
        if len(self.cache_storage) > 0:
            # Remove 25% of cache
            num_to_remove = max(1, len(self.cache_storage) // 4)
            keys_to_remove = list(self.cache_storage.keys())[:num_to_remove]
            
            for key in keys_to_remove:
                cached_data = self.cache_storage.pop(key)
                self.cache_stats["memory_usage"] -= (
                    self._estimate_tensor_size(cached_data[0]) + 
                    self._estimate_tensor_size(cached_data[1])
                )
                self.cache_stats["evictions"] += 1
    
    def clear_cache(self):
        """Clear all cached data."""
        self.cache_storage.clear()
        self.cache_stats["memory_usage"] = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
        hit_rate = self.cache_stats["hits"] / total_requests if total_requests > 0 else 0
        
        return {
            **self.cache_stats,
            "hit_rate": hit_rate,
            "cache_entries": len(self.cache_storage)
        }


class MemoryMonitor:
    """
    Continuous memory monitoring for training and inference.
    """
    
    def __init__(self, interval: float = 1.0):
        self.interval = interval
        self.monitoring = False
        self.stats_history = []
        self.monitor_thread = None
    
    def start(self):
        """Start memory monitoring."""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
    
    def stop(self):
        """Stop memory monitoring."""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_loop(self):
        """Main monitoring loop."""
        while self.monitoring:
            stats = get_memory_stats()
            stats['timestamp'] = time.time()
            self.stats_history.append(stats)
            
            # Keep only last 1000 entries
            if len(self.stats_history) > 1000:
                self.stats_history = self.stats_history[-1000:]
            
            time.sleep(self.interval)
    
    def get_history(self) -> List[Dict[str, Any]]:
        """Get memory usage history."""
        return self.stats_history.copy()
    
    def get_peak_usage(self) -> Dict[str, float]:
        """Get peak memory usage."""
        if not self.stats_history:
            return {}
        
        peak_stats = {}
        for key in self.stats_history[0].keys():
            if key != 'timestamp' and isinstance(self.stats_history[0][key], (int, float)):
                peak_stats[f"peak_{key}"] = max(stats[key] for stats in self.stats_history)
        
        return peak_stats


def get_memory_stats() -> Dict[str, float]:
    """Get comprehensive memory statistics."""
    stats = {}
    
    # GPU memory (if available)
    if torch.cuda.is_available():
        stats.update({
            "gpu_allocated_gb": torch.cuda.memory_allocated() / 1024**3,
            "gpu_reserved_gb": torch.cuda.memory_reserved() / 1024**3,
            "gpu_max_allocated_gb": torch.cuda.max_memory_allocated() / 1024**3,
            "gpu_max_reserved_gb": torch.cuda.max_memory_reserved() / 1024**3,
        })
    
    # CPU memory
    process = psutil.Process()
    memory_info = process.memory_info()
    stats.update({
        "cpu_rss_gb": memory_info.rss / 1024**3,
        "cpu_vms_gb": memory_info.vms / 1024**3,
        "cpu_percent": process.memory_percent(),
    })
    
    # System memory
    system_memory = psutil.virtual_memory()
    stats.update({
        "system_total_gb": system_memory.total / 1024**3,
        "system_available_gb": system_memory.available / 1024**3,
        "system_used_percent": system_memory.percent,
    })
    
    return stats


@contextmanager
def memory_efficient_context():
    """Context manager for memory-efficient operations."""
    # Clear cache before operation
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    
    try:
        yield
    finally:
        # Clear cache after operation
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
