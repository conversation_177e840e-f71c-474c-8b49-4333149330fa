"""
Performance and evaluation metrics for FastLLaMA.

Implements comprehensive metrics for monitoring training and inference performance,
including compression efficiency, early exit effectiveness, and memory usage.
"""

import torch
import time
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import json
import os


@dataclass
class PerformanceMetrics:
    """Performance metrics for FastLLaMA training and inference."""
    
    # Timing metrics
    total_time: float = 0.0
    forward_time: float = 0.0
    backward_time: float = 0.0
    optimizer_time: float = 0.0
    
    # Throughput metrics
    tokens_per_second: float = 0.0
    samples_per_second: float = 0.0
    
    # Memory metrics
    peak_memory_gb: float = 0.0
    avg_memory_gb: float = 0.0
    memory_efficiency: float = 0.0
    
    # Model metrics
    loss: float = 0.0
    perplexity: float = 0.0
    accuracy: float = 0.0
    
    # Efficiency metrics
    flops: int = 0
    params_active: int = 0
    params_total: int = 0
    
    # Timestamps
    timestamps: List[float] = field(default_factory=list)
    
    def update(self, **kwargs):
        """Update metrics with new values."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def add_timestamp(self):
        """Add current timestamp."""
        self.timestamps.append(time.time())
    
    def calculate_throughput(self, num_tokens: int, num_samples: int):
        """Calculate throughput metrics."""
        if self.total_time > 0:
            self.tokens_per_second = num_tokens / self.total_time
            self.samples_per_second = num_samples / self.total_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            key: getattr(self, key) for key in self.__dataclass_fields__.keys()
        }
    
    def save(self, filepath: str):
        """Save metrics to file."""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, filepath: str) -> "PerformanceMetrics":
        """Load metrics from file."""
        with open(filepath, 'r') as f:
            data = json.load(f)
        return cls(**data)


@dataclass
class CompressionMetrics:
    """Metrics for context compression effectiveness."""
    
    # Compression ratios
    compression_ratio: float = 0.0
    memory_savings: float = 0.0
    
    # Quality metrics
    compression_quality: float = 0.0
    information_retention: float = 0.0
    
    # Performance impact
    compression_time: float = 0.0
    decompression_time: float = 0.0
    compression_overhead: float = 0.0
    
    # Sequence length metrics
    original_length: int = 0
    compressed_length: int = 0
    target_length: int = 0
    
    # Quality scores per layer
    layer_quality_scores: List[float] = field(default_factory=list)
    
    def calculate_compression_ratio(self, original_length: int, compressed_length: int):
        """Calculate compression ratio and memory savings."""
        self.original_length = original_length
        self.compressed_length = compressed_length
        
        if compressed_length > 0:
            self.compression_ratio = original_length / compressed_length
            self.memory_savings = 1 - (compressed_length / original_length)
        else:
            self.compression_ratio = 0.0
            self.memory_savings = 0.0
    
    def add_quality_score(self, score: float):
        """Add quality score for a layer."""
        self.layer_quality_scores.append(score)
        self.compression_quality = np.mean(self.layer_quality_scores)
    
    def calculate_overhead(self, base_time: float):
        """Calculate compression overhead."""
        total_compression_time = self.compression_time + self.decompression_time
        if base_time > 0:
            self.compression_overhead = total_compression_time / base_time
        else:
            self.compression_overhead = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            key: getattr(self, key) for key in self.__dataclass_fields__.keys()
        }


@dataclass
class EarlyExitMetrics:
    """Metrics for early exit effectiveness."""
    
    # Exit statistics
    total_exits: int = 0
    early_exits: int = 0
    exit_rate: float = 0.0
    
    # Per-layer exit counts
    layer_exit_counts: Dict[int, int] = field(default_factory=dict)
    layer_exit_rates: Dict[int, float] = field(default_factory=dict)
    
    # Confidence metrics
    avg_confidence: float = 0.0
    confidence_threshold: float = 0.8
    confidence_scores: List[float] = field(default_factory=list)
    
    # Performance metrics
    speedup_factor: float = 0.0
    compute_savings: float = 0.0
    
    # Quality metrics
    early_exit_accuracy: float = 0.0
    full_model_accuracy: float = 0.0
    accuracy_drop: float = 0.0
    
    def record_exit(self, layer_idx: int, confidence: float, is_early: bool = True):
        """Record an exit event."""
        self.total_exits += 1
        
        if is_early:
            self.early_exits += 1
            
            # Update layer statistics
            if layer_idx not in self.layer_exit_counts:
                self.layer_exit_counts[layer_idx] = 0
            self.layer_exit_counts[layer_idx] += 1
            
            # Update confidence
            self.confidence_scores.append(confidence)
            self.avg_confidence = np.mean(self.confidence_scores)
        
        # Update exit rate
        self.exit_rate = self.early_exits / self.total_exits if self.total_exits > 0 else 0.0
    
    def calculate_layer_exit_rates(self):
        """Calculate exit rates per layer."""
        for layer_idx, count in self.layer_exit_counts.items():
            self.layer_exit_rates[layer_idx] = count / self.total_exits if self.total_exits > 0 else 0.0
    
    def calculate_speedup(self, total_layers: int):
        """Calculate speedup factor from early exits."""
        if self.total_exits == 0:
            self.speedup_factor = 1.0
            return
        
        # Calculate average layers used
        total_layer_usage = 0
        for layer_idx, count in self.layer_exit_counts.items():
            total_layer_usage += layer_idx * count
        
        # Add full model usage for non-early exits
        full_model_exits = self.total_exits - self.early_exits
        total_layer_usage += total_layers * full_model_exits
        
        avg_layers_used = total_layer_usage / self.total_exits
        self.speedup_factor = total_layers / avg_layers_used
        self.compute_savings = 1 - (avg_layers_used / total_layers)
    
    def calculate_accuracy_impact(self, early_exit_correct: int, full_model_correct: int):
        """Calculate accuracy impact of early exits."""
        if self.early_exits > 0:
            self.early_exit_accuracy = early_exit_correct / self.early_exits
        
        if self.total_exits > 0:
            self.full_model_accuracy = full_model_correct / self.total_exits
        
        self.accuracy_drop = self.full_model_accuracy - self.early_exit_accuracy
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            key: getattr(self, key) for key in self.__dataclass_fields__.keys()
        }


class MetricsCollector:
    """
    Comprehensive metrics collector for FastLLaMA.
    
    Collects and aggregates performance, compression, and early exit metrics
    during training and inference.
    """
    
    def __init__(self, config):
        self.config = config
        self.performance_metrics = PerformanceMetrics()
        self.compression_metrics = CompressionMetrics()
        self.early_exit_metrics = EarlyExitMetrics()
        
        # Timing context
        self.start_times = {}
        
        # Aggregated metrics
        self.step_metrics = []
        self.epoch_metrics = []
    
    def start_timer(self, name: str):
        """Start a timer for a specific operation."""
        self.start_times[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """End a timer and return elapsed time."""
        if name in self.start_times:
            elapsed = time.time() - self.start_times[name]
            del self.start_times[name]
            return elapsed
        return 0.0
    
    def record_step_metrics(
        self,
        loss: float,
        num_tokens: int,
        memory_usage: Dict[str, float],
        compression_info: Optional[Dict] = None,
        early_exit_info: Optional[Dict] = None
    ):
        """Record metrics for a training/inference step."""
        # Update performance metrics
        self.performance_metrics.loss = loss
        self.performance_metrics.perplexity = torch.exp(torch.tensor(loss)).item()
        self.performance_metrics.peak_memory_gb = memory_usage.get("peak_memory_gb", 0.0)
        
        # Record compression metrics
        if compression_info:
            self.compression_metrics.calculate_compression_ratio(
                compression_info.get("original_length", 0),
                compression_info.get("compressed_length", 0)
            )
            if "quality_scores" in compression_info:
                for score in compression_info["quality_scores"]:
                    self.compression_metrics.add_quality_score(score)
        
        # Record early exit metrics
        if early_exit_info:
            for exit_data in early_exit_info.get("exits", []):
                self.early_exit_metrics.record_exit(
                    exit_data["layer_idx"],
                    exit_data["confidence"],
                    exit_data.get("is_early", True)
                )
        
        # Store step metrics
        step_data = {
            "performance": self.performance_metrics.to_dict(),
            "compression": self.compression_metrics.to_dict(),
            "early_exit": self.early_exit_metrics.to_dict(),
            "timestamp": time.time()
        }
        self.step_metrics.append(step_data)
    
    def calculate_epoch_metrics(self) -> Dict[str, Any]:
        """Calculate aggregated metrics for the epoch."""
        if not self.step_metrics:
            return {}
        
        # Aggregate performance metrics
        losses = [step["performance"]["loss"] for step in self.step_metrics]
        perplexities = [step["performance"]["perplexity"] for step in self.step_metrics]
        memory_usage = [step["performance"]["peak_memory_gb"] for step in self.step_metrics]
        
        # Aggregate compression metrics
        compression_ratios = [step["compression"]["compression_ratio"] for step in self.step_metrics if step["compression"]["compression_ratio"] > 0]
        compression_qualities = [step["compression"]["compression_quality"] for step in self.step_metrics if step["compression"]["compression_quality"] > 0]
        
        # Aggregate early exit metrics
        exit_rates = [step["early_exit"]["exit_rate"] for step in self.step_metrics]
        confidence_scores = []
        for step in self.step_metrics:
            confidence_scores.extend(step["early_exit"]["confidence_scores"])
        
        epoch_metrics = {
            "performance": {
                "avg_loss": np.mean(losses),
                "avg_perplexity": np.mean(perplexities),
                "avg_memory_gb": np.mean(memory_usage),
                "max_memory_gb": np.max(memory_usage),
            },
            "compression": {
                "avg_compression_ratio": np.mean(compression_ratios) if compression_ratios else 0.0,
                "avg_compression_quality": np.mean(compression_qualities) if compression_qualities else 0.0,
            },
            "early_exit": {
                "avg_exit_rate": np.mean(exit_rates),
                "avg_confidence": np.mean(confidence_scores) if confidence_scores else 0.0,
            },
            "num_steps": len(self.step_metrics)
        }
        
        self.epoch_metrics.append(epoch_metrics)
        return epoch_metrics
    
    def reset_step_metrics(self):
        """Reset step-level metrics."""
        self.step_metrics = []
    
    def save_metrics(self, output_dir: str, epoch: Optional[int] = None):
        """Save all metrics to files."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save step metrics
        step_file = os.path.join(output_dir, f"step_metrics_epoch_{epoch}.json" if epoch is not None else "step_metrics.json")
        with open(step_file, 'w') as f:
            json.dump(self.step_metrics, f, indent=2)
        
        # Save epoch metrics
        epoch_file = os.path.join(output_dir, "epoch_metrics.json")
        with open(epoch_file, 'w') as f:
            json.dump(self.epoch_metrics, f, indent=2)
        
        # Save individual metric objects
        self.performance_metrics.save(os.path.join(output_dir, "performance_metrics.json"))
        
        with open(os.path.join(output_dir, "compression_metrics.json"), 'w') as f:
            json.dump(self.compression_metrics.to_dict(), f, indent=2)
        
        with open(os.path.join(output_dir, "early_exit_metrics.json"), 'w') as f:
            json.dump(self.early_exit_metrics.to_dict(), f, indent=2)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        latest_epoch = self.epoch_metrics[-1] if self.epoch_metrics else {}
        
        return {
            "latest_epoch": latest_epoch,
            "total_epochs": len(self.epoch_metrics),
            "total_steps": len(self.step_metrics),
            "current_performance": self.performance_metrics.to_dict(),
            "current_compression": self.compression_metrics.to_dict(),
            "current_early_exit": self.early_exit_metrics.to_dict(),
        }
