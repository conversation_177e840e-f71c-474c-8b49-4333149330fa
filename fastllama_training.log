2025-05-30 11:19:43,502 - __main__ - INFO - Starting FastLLaMA training
2025-05-30 11:19:43,504 - __main__ - INFO - Distributed training: rank=0, world_size=1, local_rank=-1
2025-05-30 11:19:43,857 - __main__ - INFO - Created FastLLaMA model with 25685531 parameters
2025-05-30 11:19:43,858 - __main__ - INFO - Created datasets: train=50, eval=10
2025-05-30 11:19:43,869 - __main__ - INFO - Memory optimizations applied
2025-05-30 11:19:46,001 - __main__ - INFO - Starting training...
2025-05-30 11:19:46,002 - fastllama.training.trainer - INFO - Starting FastLLaMA training...
2025-05-30 11:19:46,002 - fastllama.training.trainer - INFO - Starting foundation phase...
2025-05-30 11:19:46,002 - __main__ - ERROR - Training failed with error: 'PhaseConfig' object has no attribute 'get'
2025-05-30 11:22:04,499 - __main__ - INFO - Starting FastLLaMA training
2025-05-30 11:22:04,499 - __main__ - INFO - Distributed training: rank=0, world_size=1, local_rank=-1
2025-05-30 11:22:04,824 - __main__ - INFO - Created FastLLaMA model with 25685531 parameters
2025-05-30 11:22:04,825 - __main__ - INFO - Created datasets: train=50, eval=10
2025-05-30 11:22:04,835 - __main__ - INFO - Memory optimizations applied
2025-05-30 11:22:06,298 - __main__ - INFO - Starting training...
2025-05-30 11:22:06,298 - fastllama.training.trainer - INFO - Starting FastLLaMA training...
2025-05-30 11:22:06,298 - fastllama.training.trainer - INFO - Starting foundation phase...
2025-05-30 11:22:10,004 - __main__ - ERROR - Training failed with error: Expected input batch_size (12) to match target batch_size (255).
2025-05-30 11:22:44,839 - __main__ - INFO - Starting FastLLaMA training
2025-05-30 11:22:44,841 - __main__ - INFO - Distributed training: rank=0, world_size=1, local_rank=-1
2025-05-30 11:22:45,161 - __main__ - INFO - Created FastLLaMA model with 25685531 parameters
2025-05-30 11:22:45,161 - __main__ - INFO - Created datasets: train=20, eval=5
2025-05-30 11:22:45,172 - __main__ - INFO - Memory optimizations applied
2025-05-30 11:22:46,464 - __main__ - INFO - Starting training...
2025-05-30 11:22:46,464 - fastllama.training.trainer - INFO - Starting FastLLaMA training...
2025-05-30 11:22:46,464 - fastllama.training.trainer - INFO - Starting foundation phase...
2025-05-30 11:22:49,327 - fastllama.training.trainer - INFO - Step 2: loss=6.8757, lr=0.00e+00, seq_len=2048, memory=0.42GB
2025-05-30 11:22:49,626 - fastllama.training.trainer - INFO - Step 4: loss=6.6738, lr=0.00e+00, seq_len=2048, memory=0.43GB
2025-05-30 11:22:49,918 - fastllama.training.trainer - INFO - Step 6: loss=7.0059, lr=0.00e+00, seq_len=2048, memory=0.43GB
2025-05-30 11:22:50,204 - fastllama.training.trainer - INFO - Step 8: loss=7.1074, lr=0.00e+00, seq_len=2048, memory=0.43GB
2025-05-30 11:22:50,503 - fastllama.training.trainer - INFO - Step 10: loss=7.0046, lr=0.00e+00, seq_len=2048, memory=0.43GB
2025-05-30 11:22:53,377 - fastllama.training.trainer - INFO - Eval loss: 7.0013
2025-05-30 11:22:53,946 - fastllama.training.trainer - INFO - Checkpoint saved to ./fastllama_output\checkpoint-10
2025-05-30 11:22:54,379 - fastllama.training.trainer - INFO - Step 12: loss=7.0501, lr=0.00e+00, seq_len=2048, memory=0.43GB
2025-05-30 11:22:54,803 - fastllama.training.trainer - INFO - Step 14: loss=6.8372, lr=0.00e+00, seq_len=2048, memory=0.43GB
2025-05-30 11:22:55,394 - fastllama.training.trainer - INFO - Starting long_context phase...
2025-05-30 11:22:57,994 - fastllama.training.trainer - INFO - Step 16: loss=7.1771, lr=0.00e+00, seq_len=128, memory=0.43GB
2025-05-30 11:22:58,277 - fastllama.training.trainer - INFO - Step 18: loss=6.9440, lr=0.00e+00, seq_len=128, memory=0.43GB
2025-05-30 11:22:58,830 - fastllama.training.trainer - INFO - Starting efficiency phase...
2025-05-30 11:23:01,352 - fastllama.training.trainer - INFO - Step 20: loss=6.8867, lr=0.00e+00, seq_len=128, memory=0.43GB
2025-05-30 11:23:04,114 - fastllama.training.trainer - INFO - Eval loss: 6.8858
2025-05-30 11:23:04,682 - fastllama.training.trainer - INFO - Checkpoint saved to ./fastllama_output\checkpoint-20
2025-05-30 11:23:05,127 - fastllama.training.trainer - INFO - Checkpoint saved to ./fastllama_output\checkpoint-20
2025-05-30 11:23:05,820 - fastllama.training.trainer - INFO - Training completed!
2025-05-30 11:23:05,820 - __main__ - INFO - Training completed successfully!
2025-05-30 11:23:05,820 - __main__ - INFO - Final metrics: {'loss': [6.875650882720947, 6.673828125, 7.005859375, 7.107421875, 7.004557132720947, 7.050130367279053, 6.837239742279053, 7.177083492279053, 6.944010257720947, 6.88671875], 'learning_rate': [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], 'sequence_length': [2048, 2048, 2048, 2048, 2048, 2048, 2048, 128, 128, 128], 'memory_usage': [0.41887664794921875, 0.4296722412109375, 0.4296722412109375, 0.4296722412109375, 0.4296722412109375, 0.4296722412109375, 0.4296722412109375, 0.4296722412109375, 0.4296722412109375, 0.4296722412109375], 'throughput': []}
2025-05-30 11:23:05,829 - __main__ - INFO - Final memory usage: {'gpu_allocated_gb': 0.28937721252441406, 'gpu_reserved_gb': 0.466796875, 'gpu_max_allocated_gb': 0.33493900299072266, 'gpu_max_reserved_gb': 0.466796875, 'cpu_rss_gb': 1.4152793884277344, 'cpu_vms_gb': 3.0768508911132812, 'cpu_percent': 4.447848402922864, 'system_total_gb': 31.819416046142578, 'system_available_gb': 16.264659881591797, 'system_used_percent': 48.9}
