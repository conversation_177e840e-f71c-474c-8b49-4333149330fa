"""
Fix for FastLLaMA training issues.

This script addresses the major issues found:
1. Excessive padding in data samples
2. Loss calculation on padding tokens
3. Unused model parameters
"""

import os
import sys
import torch
import logging
from pathlib import Path

# Add FastLLaMA to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastllama import FastLLaMAConfig, FastLLaMAModel
from fastllama.training import FastLLaMATrainer, TrainingArguments
from fastllama.utils import get_memory_stats
from fastllama.data import DataConfig
from transformers import AutoTokenizer

def setup_logging():
    """Setup basic logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def create_fixed_data_config():
    """Create improved data configuration."""
    return DataConfig(
        dataset_name="HuggingFaceTB/smollm-corpus",
        dataset_config="fineweb-edu-dedup",
        tokenizer_name="D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer",
        max_length=512,  # Reduced from 1024 to minimize padding
        batch_size=4,    # Increased batch size since sequences are shorter
        streaming=True,
        num_workers=2,
        min_length=400,  # Increased minimum length to reduce padding
        padding="longest",  # Use dynamic padding instead of max_length
        truncation=True,
        filter_empty=True,
    )

def create_simplified_model_config(tokenizer_vocab_size):
    """Create a simplified model configuration for better training."""
    return FastLLaMAConfig(
        # Model architecture
        hidden_size=768,
        intermediate_size=768*4,
        num_attention_heads=8,
        num_key_value_heads=8,  # Use standard attention instead of GQA
        num_hidden_layers=8,
        vocab_size=tokenizer_vocab_size + 8,
        max_position_embeddings=2048,

        # Disable complex features that might interfere with training
        enable_context_compression=False,
        enable_early_exit=False,
        use_gradient_checkpointing=True,  # Keep for memory efficiency
        use_mixed_precision=True,         # Keep for speed

        # Simplified attention
        local_attention_window=512,
        sparse_attention_stride=1,  # Disable sparse attention

        # Disable parameter sharing and quantization for now
        parameter_sharing=False,
        kv_cache_quantization=False,

        # Set early exit layers to empty
        early_exit_layers=[],
        confidence_threshold=0.8,
    )

def create_improved_training_args(output_dir):
    """Create improved training arguments."""
    return TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=1,
        max_steps=10000,  # Reduced for testing
        per_device_train_batch_size=4,
        per_device_eval_batch_size=4,
        gradient_accumulation_steps=2,  # Effective batch size = 4 * 2 = 8

        # Learning rate - start with a more conservative value
        learning_rate=1e-4,  # Reduced from 5e-4
        weight_decay=0.01,
        warmup_steps=500,   # Increased warmup

        # Memory optimizations
        use_mixed_precision=True,
        gradient_checkpointing=True,
        max_grad_norm=1.0,

        # Sequence length - start simple
        initial_seq_length=512,
        max_seq_length=512,  # Keep constant for now
        seq_length_warmup_steps=0,  # Disable sequence length scaling

        # Evaluation and logging
        eval_steps=1000,
        save_steps=2000,
        logging_steps=50,  # More frequent logging

        # Simplified phase-based training
        foundation_phase_ratio=1.0,  # Only foundation phase for now
        long_context_phase_ratio=0.0,
        efficiency_phase_ratio=0.0,

        # Disable early exit training
        early_exit_loss_weight=0.0,
        confidence_loss_weight=0.0,

        # Data configuration
        dataset_name="HuggingFaceTB/smollm-corpus",
        dataset_config="fineweb-edu-dedup",
        tokenizer_name="D:\\vlinhd11\\Projects\\LCMS_LLM\\MOE-LLM\\v2\\my_tokenizer\\llama_en_vi_tokenizer",
        streaming=True,
        text_column="text",
        filter_empty=True,
        min_length=400,
    )

def main():
    logger = setup_logging()
    logger.info("🚀 Fixed FastLLaMA Training")

    # Configuration
    output_dir = "./fastllama_fixed_output"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # 1. Create improved data configuration
    logger.info("📚 Creating improved data configuration...")
    data_config = create_fixed_data_config()

    # Create tokenizer
    tokenizer = AutoTokenizer.from_pretrained(data_config.tokenizer_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    logger.info(f"✅ Tokenizer loaded. Vocab size: {tokenizer.vocab_size}")

    # 2. Create simplified model
    logger.info("🧠 Creating simplified FastLLaMA model...")
    config = create_simplified_model_config(tokenizer.vocab_size)
    model = FastLLaMAModel(config)
    model.to(device)

    num_params = sum(p.numel() for p in model.parameters())
    logger.info(f"✅ Model created with {num_params/1e6:.1f}M parameters")

    # 3. Setup improved training arguments
    logger.info("⚙️ Setting up improved training configuration...")
    training_args = create_improved_training_args(output_dir)

    # 4. Create trainer
    logger.info("🏋️ Creating trainer...")
    trainer = FastLLaMATrainer(
        model=model,
        config=config,
        args=training_args,
        tokenizer=tokenizer,
        data_config=data_config,
    )

    # 5. Start training
    logger.info("🚀 Starting improved training...")
    initial_memory = get_memory_stats()
    logger.info(f"Initial memory usage: {initial_memory}")

    try:
        training_metrics = trainer.train()

        logger.info("🎉 Training completed successfully!")
        logger.info(f"Final training metrics: {training_metrics}")

        # Print memory usage
        final_memory = get_memory_stats()
        logger.info(f"Final memory usage: {final_memory}")

        # Save model
        model_path = Path(output_dir) / "final_model"
        model_path.mkdir(parents=True, exist_ok=True)
        torch.save(model.state_dict(), model_path / "pytorch_model.bin")
        config.save_pretrained(model_path)
        tokenizer.save_pretrained(model_path)

        logger.info(f"✅ Model saved to {model_path}")

        return model, tokenizer, training_metrics

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        raise

    finally:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

if __name__ == "__main__":
    model, tokenizer, metrics = main()
    print("\n🎉 Fixed training completed!")
    print(f"📊 Final metrics: {metrics}")
    print(f"🧠 Model parameters: {sum(p.numel() for p in model.parameters())/1e6:.1f}M")
    print("🚀 Ready for inference or further training!")
