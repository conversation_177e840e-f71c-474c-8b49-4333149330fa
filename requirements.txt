# Core dependencies
torch>=2.0.0
numpy>=1.21.0
transformers>=4.30.0

# Training and optimization
accelerate>=0.20.0
# deepspeed>=0.9.0
bitsandbytes>=0.39.0

# Data processing
datasets>=2.12.0
tokenizers>=0.13.0

# Monitoring and logging
wandb>=0.15.0
tensorboard>=2.13.0
tqdm>=4.65.0

# Utilities
psutil>=5.9.0
matplotlib>=3.7.0
seaborn>=0.12.0
pandas>=2.0.0

# Development and testing
pytest>=7.3.0
black>=23.3.0
flake8>=6.0.0
mypy>=1.3.0

# Optional: Flash Attention (if available)
# flash-attn>=2.0.0

# Optional: Triton for custom kernels
# triton>=2.0.0
