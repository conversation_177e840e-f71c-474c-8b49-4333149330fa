"""
Simple test script to verify FastLLaMA implementation works correctly.
"""

import torch
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastllama import FastLLaMAConfig, FastLLaMAModel, FastLLaMAInferenceEngine
from fastllama.inference import GenerationConfig


def test_model_creation():
    """Test basic model creation and forward pass."""
    print("Testing model creation...")

    # Create a small configuration for testing
    config = FastLLaMAConfig(
        hidden_size=512,
        intermediate_size=1024,
        num_attention_heads=8,
        num_key_value_heads=2,
        num_hidden_layers=4,
        vocab_size=1000,
        max_position_embeddings=2048,

        # Enable FastLLaMA features
        enable_context_compression=True,
        enable_early_exit=True,
        use_gradient_checkpointing=False,  # Disable for testing
        kv_cache_quantization=False,  # Disable for testing
        parameter_sharing=False,  # Disable for testing

        # Adjust early exit layers for small model
        early_exit_layers=[2, 3],  # Use layers that exist in our 4-layer model
    )

    # Create model
    model = FastLLaMAModel(config)
    model.eval()

    print(f"✓ Model created with {sum(p.numel() for p in model.parameters())} parameters")

    # Test forward pass
    batch_size = 2
    seq_length = 64
    input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_length))

    with torch.no_grad():
        outputs = model(input_ids=input_ids)

    print(f"✓ Forward pass successful")
    print(f"  Input shape: {input_ids.shape}")
    print(f"  Output logits shape: {outputs['logits'].shape}")

    return model, config


def test_inference_engine():
    """Test inference engine functionality."""
    print("\nTesting inference engine...")

    model, config = test_model_creation()

    # Create inference engine
    device = torch.device("cpu")  # Use CPU for testing
    engine = FastLLaMAInferenceEngine(model, config, device, enable_speculative_decoding=False)

    # Test generation
    input_ids = torch.randint(0, config.vocab_size, (1, 10))

    generation_config = GenerationConfig(
        max_new_tokens=20,
        temperature=1.0,
        top_k=50,
        top_p=0.9,
        do_sample=True,
        enable_early_exit=False,  # Disable for testing
        use_cache=False,  # Disable cache for testing
    )

    outputs = engine.generate(
        input_ids=input_ids,
        generation_config=generation_config
    )

    print(f"✓ Text generation successful")
    print(f"  Input length: {input_ids.shape[1]}")
    print(f"  Generated length: {outputs.sequences.shape[1]}")
    print(f"  Generation stats: {outputs.generation_stats}")

    return engine


def test_attention_mechanisms():
    """Test individual attention mechanisms."""
    print("\nTesting attention mechanisms...")

    from fastllama.models.attention import LocalAttention, SparseAttention, CompressedAttention

    config = FastLLaMAConfig(
        hidden_size=256,
        num_attention_heads=4,
        num_key_value_heads=2,  # Add this to fix the validation error
        local_attention_window=32,
        sparse_attention_stride=4,
        compression_ratio=8,
    )

    batch_size = 2
    seq_length = 64
    hidden_states = torch.randn(batch_size, seq_length, config.hidden_size)

    # Test Local Attention
    local_attn = LocalAttention(config)
    local_outputs = local_attn(hidden_states)
    print(f"✓ Local attention: {hidden_states.shape} -> {local_outputs[0].shape}")

    # Test Sparse Attention
    sparse_attn = SparseAttention(config)
    sparse_outputs = sparse_attn(hidden_states)
    print(f"✓ Sparse attention: {hidden_states.shape} -> {sparse_outputs[0].shape}")

    # Test Compressed Attention
    compressed_attn = CompressedAttention(config)
    compressed_outputs = compressed_attn(hidden_states)
    print(f"✓ Compressed attention: {hidden_states.shape} -> {compressed_outputs[0].shape}")


def test_context_compression():
    """Test context compression module."""
    print("\nTesting context compression...")

    from fastllama.models.compression import ContextCompressor

    config = FastLLaMAConfig(
        hidden_size=256,
        compression_ratio=8,
        compression_encoder_layers=2,
        progressive_compression=True,
    )

    compressor = ContextCompressor(config)

    # Test compression
    batch_size = 1
    seq_length = 64
    hidden_states = torch.randn(batch_size, seq_length, config.hidden_size)

    compressed_states, quality = compressor(hidden_states)
    print(f"✓ Context compression: {hidden_states.shape} -> {compressed_states.shape}")
    print(f"  Compression quality: {quality.mean().item():.3f}")

    # Test decompression
    decompressed_states = compressor.decompress(compressed_states, target_length=seq_length)
    print(f"✓ Context decompression: {compressed_states.shape} -> {decompressed_states.shape}")


def test_memory_utilities():
    """Test memory optimization utilities."""
    print("\nTesting memory utilities...")

    from fastllama.utils.memory import get_memory_stats, MemoryOptimizer

    # Test memory stats
    stats = get_memory_stats()
    print(f"✓ Memory stats collected: {list(stats.keys())}")

    # Test memory optimizer (basic functionality)
    config = FastLLaMAConfig(
        hidden_size=256,
        num_hidden_layers=4,
        early_exit_layers=[2, 3]  # Fix early exit layers
    )
    model = FastLLaMAModel(config)

    optimizer = MemoryOptimizer(model, config)
    optimizer.optimize_for_inference()
    print(f"✓ Memory optimizer applied")


def test_metrics():
    """Test metrics collection."""
    print("\nTesting metrics...")

    from fastllama.utils.metrics import PerformanceMetrics, CompressionMetrics, EarlyExitMetrics

    # Test performance metrics
    perf_metrics = PerformanceMetrics()
    perf_metrics.update(loss=0.5, tokens_per_second=100.0)
    print(f"✓ Performance metrics: {perf_metrics.to_dict()}")

    # Test compression metrics
    comp_metrics = CompressionMetrics()
    comp_metrics.calculate_compression_ratio(1000, 100)
    print(f"✓ Compression metrics: compression_ratio={comp_metrics.compression_ratio:.1f}")

    # Test early exit metrics
    exit_metrics = EarlyExitMetrics()
    exit_metrics.record_exit(layer_idx=12, confidence=0.85, is_early=True)
    print(f"✓ Early exit metrics: exit_rate={exit_metrics.exit_rate:.2f}")


def main():
    """Run all tests."""
    print("FastLLaMA Implementation Test Suite")
    print("=" * 50)

    try:
        # Basic functionality tests
        test_model_creation()
        test_inference_engine()

        # Component tests
        test_attention_mechanisms()
        test_context_compression()

        # Utility tests
        test_memory_utilities()
        test_metrics()

        print("\n" + "=" * 50)
        print("✅ All tests passed! FastLLaMA implementation is working correctly.")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
